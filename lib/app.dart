import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/features/activity/data/firebase_activity_repo.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/bill/data/firebase_bill_repo.dart';
import 'package:cp_associates/features/bill/presentation/cubit/bill_cubit.dart';
import 'package:cp_associates/features/document/data/firebase_document_repo.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_cubit.dart';
import 'package:cp_associates/features/project/data/fb_project_repo.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:cp_associates/features/task/data/firebase_task_repo.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/users/data/firebase_user_repo.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'core/theme/app_theme.dart';
import 'core/widgets/responsive_widget.dart';
import 'features/auth/data/fb_auth_repo.dart';
import 'features/auth/presentation/cubits/auth_cubit.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  final firebaseAuthRepo = FirebaseAuthRepo();
  final firebaseProjectRepo = FirebaseProjectRepo();
  final firebaseUserRepo = FirebaseUserRepo();

  final firebaseActivityRepo = FirebaseActivityRepo();
  final firebaseDocumentRepo = FirebaseDocumentRepo();
  final firebaseStroageRepo = FirebaseStorageRepo();
  final firebaseBillRepo = FbBillRepo();
  final firebaseTaskRepo = FirebaseTaskRepo();

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AuthCubit>(
          create: (_) => AuthCubit(authRepo: firebaseAuthRepo)..checkAuth(),
        ),
        BlocProvider<ProjectCubit>(
          create: (_) => ProjectCubit(firebaseProjectRepo),
        ),
        BlocProvider<ActivityCubit>(
          create: (_) => ActivityCubit(firebaseActivityRepo),
        ),
        BlocProvider<BillCubit>(
          create: (_) => BillCubit(repo: firebaseBillRepo),
        ),
        BlocProvider<DocumentCubit>(
          create: (_) => DocumentCubit(documentRepo: firebaseDocumentRepo),
        ),
        BlocProvider<TaskCubit>(
          create: (_) => TaskCubit(taskRepo: firebaseTaskRepo),
        ),

        BlocProvider(
          create: (context) => UserCubit(firebaseUserRepo)..fetchAllUsers(),
        ),
      ],
      child: BlocListener<AuthCubit, AuthState>(
        listener: (context, state) {},
        child: ResponsiveWid(
          mobile: ScreenUtilInit(
            designSize: const Size(430, 932),
            minTextAdapt: true,
            builder:
                (context, _) => Builder(
                  builder:
                      (routerContext) => MaterialApp.router(
                        debugShowCheckedModeBanner: false,
                        theme: AppTheme.lightTheme,

                        routerConfig: appRoute,
                      ),
                ),
          ),
          desktop: Builder(
            builder:
                (routerContext) => MaterialApp.router(
                  debugShowCheckedModeBanner: false,
                  theme: AppTheme.lightTheme,
                  routerConfig: appRoute,
                ),
          ),
        ),
      ),
    );
  }
}
