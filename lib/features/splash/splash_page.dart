import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  void initState() {
    context.read<AuthCubit>().checkAuth();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthCubit, AuthState>(
      listener: (context, state) {
        if (state.isAuthenticated) {
          //AFTER 2 -3 SEOND THEN REDIRECT
          Future.delayed(Duration(seconds: 2), () {
            context.go(Routes.home);
          });
        } else {
          context.go(Routes.login);
        }
      },
      child: Scaffold(
        body: Container(
          //FADEIN FOUNT EFFECT
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            //FADEIN FOUNT EFFECT
            gradient: LinearGradient(
              colors: [AppColors.primary, AppColors.secondary],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          ),
          child: Center(
            child: Image.asset(
              "assets/images/logo_light.png",
              width: MediaQuery.of(context).size.width * 0.4,
              fit: BoxFit.contain,
            ),
          ),
        ),
      ),
    );
  }
}
