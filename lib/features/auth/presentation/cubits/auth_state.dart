import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:flutter/foundation.dart';

class AuthState {
  bool isRegistered;
  bool isAuthenticated;
  UserModel? user;
  String message;
  bool loading;

  AuthState({
    required this.isRegistered,
    required this.isAuthenticated,
    required this.message,
    required this.loading,
    required this.user,
  });

  factory AuthState.initial() {
    return AuthState(
      isRegistered: false,
      isAuthenticated: false,
      user: null,
      message: "",
      loading: false,
    );
  }
  AuthState copyWith({
    bool? isRegistered,
    bool? isAuthenticated,
    UserModel? user,
    String? message,
    bool? loading,
  }) {
    return AuthState(
      isRegistered: isRegistered ?? this.isRegistered,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      message: message ?? this.message,
      loading: loading ?? this.loading,
    );
  }
}

// //inital state
// class AuthInitialState extends AuthState {}

// //loading state
// class AuthLoading extends AuthState {}

// //authnticated state
// class Authenticated extends AuthState {
//   final UserModel user;
//   Authenticated(this.user);
// }

// //unauthnticated state
// class UnAuthenticated extends AuthState {}

// class UserRegistered extends AuthState {
//   final String email;
//   UserRegistered(this.email);
// }

// //erros state
// class AuthError extends AuthState {
//   String msg;
//   AuthError(this.msg);
// }
