import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/domain/repo/auth_repo.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/router.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepo authRepo;
  UserModel? _currentUser;
  AuthCubit({required this.authRepo}) : super(AuthState.initial());

  //check user is alredy authenticated
  void checkAuth() async {
    final UserModel? user = await authRepo.getCurrentUser();
    // print(user);
    if (user != null) {
      _currentUser = user;
      emit(state.copyWith(isAuthenticated: true, user: user));
    } else {
      emit(state.copyWith(isAuthenticated: false));
    }
  }

  //get current
  UserModel? get currentUser => _currentUser;

  //login with email and password
  Future<void> login(String email, String password) async {
    try {
      emit(state.copyWith(loading: true));
      final user = await authRepo.loginWithEmailPassword(email, password);
      if (user != null) {
        _currentUser = user;
        emit(
          state.copyWith(
            loading: false,
            user: user,
            isAuthenticated: true,
            message: "loggedin sucessfully",
          ),
        );
      } else {
        emit(
          state.copyWith(user: null, isAuthenticated: false, loading: false),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          isAuthenticated: false,
          message: e.toString(),
          loading: false,
        ),
      );
    }
  }

  //register with email and password
  Future<void> register(
    String email,
    String password,
    String name,
    String role,
  ) async {
    try {
      emit(state.copyWith(loading: true));
      final user = await authRepo.registerWithEmailPassword(
        name,
        email,
        password,
        role,
      );
      emit(
        state.copyWith(
          message: "New user register sucessfully",
          isRegistered: true,
          loading: false,
        ),
      );
    } catch (e) {
      emit(
        state.copyWith(
          message: e.toString(),
          isRegistered: false,
          loading: false,
        ),
      );
    }
  }

  //logout
  Future<void> logOut() async {
    emit(state.copyWith(loading: true));
    await authRepo.logout();
    emit(
      state.copyWith(
        isAuthenticated: false,
        loading: false,
        message: "Logout sucessfully",
      ),
    );
  }
}
