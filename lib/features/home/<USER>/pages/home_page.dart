import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/features/home/<USER>/widgets/attendance_container.dart';
import 'package:cp_associates/features/home/<USER>/widgets/common_appbar.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/project_form.dart';
import 'package:cp_associates/features/home/<USER>/widgets/project_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../auth/presentation/cubits/auth_state.dart';

class HomePage extends StatefulWidget {
  HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  Widget build(BuildContext context) {
    // print("auth:${context.read<AuthCubit>().state.user}");
    // print("auth:${context.read<AuthCubit>().state.isAuthenticated}");
    return BlocListener<ProjectCubit, ProjectState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      child: ResponsiveWid(
        mobile: Scaffold(
          appBar: PreferredSize(
            child: CommonAppBar(),
            preferredSize: Size.fromHeight(60),
          ),

          body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SingleChildScrollView(
              child: Center(
                child: Container(
                  constraints: BoxConstraints(
                    maxWidth: MediaQuery.sizeOf(context).width,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(height: 16),
                      AttendanceContainer(isMobile: true),
                      SizedBox(height: 30),
                      projectHeading(context),
                      ProjectContainer(isMobile: true),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
        desktop: Scaffold(
          appBar: PreferredSize(
            child: CommonAppBar(),
            preferredSize: Size.fromHeight(60),
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(100),
              child: Column(
                // crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Container(
                          child: AttendanceContainer(isMobile: false),
                        ),
                      ),
                      SizedBox(width: 20),
                      BlocBuilder<AuthCubit, AuthState>(
                        builder: (context, state) {
                          if (state.isAuthenticated) {
                            final user = state.user;

                            return Expanded(
                              flex: 1,
                              child: Container(
                                padding: EdgeInsets.all(40),
                                height: 240,
                                decoration: BoxDecoration(
                                  color: AppColors.containerGreyColor,
                                  border: Border.all(
                                    color: AppColors.borderGrey,
                                  ),
                                  borderRadius: BorderRadius.circular(13),
                                ),
                                child: Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: 80,
                                          height: 80,
                                          decoration: BoxDecoration(
                                            color: AppColors.containerGreyColor,
                                            border: Border.all(
                                              color: AppColors.borderGrey,
                                            ),
                                            shape: BoxShape.circle,
                                          ),
                                          child: Center(
                                            child: Text(
                                              user?.email[0].toUpperCase() ??
                                                  "",
                                              style: TextStyle(
                                                fontSize: 50,
                                                color: AppColors.black,
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(height: 40),
                                        Text(
                                          user?.name ?? "",
                                          style: AppTextStyles.heading2,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          } else {
                            return SizedBox(); // Or show loading/error/etc based on other states
                          }
                        },
                      ),
                    ],
                  ),
                  SizedBox(height: 20),
                  projectHeading(context),
                  ProjectContainer(isMobile: false),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Row projectHeading(BuildContext context) {
    return Row(
      children: [
        Text("My Projects", style: AppTextStyles.heading2),
        Spacer(),
        IconButton(
          onPressed: () {
            showModalBottomSheet(
              context: context,
              // isScrollControlled: true,
              builder: (context) {
                return Padding(
                  padding: EdgeInsets.all(16),
                  child: BlocProvider(
                    create:
                        (context) =>
                            ProjectFormCubit(context.read<ProjectCubit>().repo),
                    child: SingleChildScrollView(
                      child: ProjectForm(editProject: null),
                    ),
                  ),
                );
              },
            );
            // : showProjectBottomSheet(context, null);
          },
          icon: Icon(CupertinoIcons.add),
        ),
      ],
    );
  }
}
