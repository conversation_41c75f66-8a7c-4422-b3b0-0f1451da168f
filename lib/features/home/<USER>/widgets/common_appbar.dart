import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_state.dart';
import 'package:cp_associates/features/auth/presentation/pages/registration_page.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class CommonAppBar extends StatelessWidget {
  const CommonAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context).width;
    return AppBar(
      title: BlocBuilder<AuthCubit, AuthState>(
        builder: (context, state) {
          String userEmailInitial = "U"; // default placeholder

          if (state.isAuthenticated) {
            userEmailInitial = state.user?.email[0].toUpperCase() ?? "";
          }

          return Row(
            children: [
              Image.asset(
                "assets/images/logo_light.png",
                color: Colors.black,
                width: 66,
                height: 33,
              ),
              Spacer(),
              Container(
                padding: EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: AppColors.containerGreyColor,
                  border: Border.all(color: AppColors.borderGrey),
                  shape: BoxShape.circle,
                ),
                child: Icon(CupertinoIcons.bell, size: 20),
              ),
              SizedBox(width: 10),
              InkWell(
                onTap: () {
                  // final authCubit = context.read<AuthCubit>();

                  kIsWeb
                      ? context.go(Routes.registration)
                      : context.push(Routes.registration);

                  // Navigator.push(
                  //   context,
                  //   MaterialPageRoute(
                  //     builder:
                  //         (context) => BlocProvider.value(
                  //           value: authCubit,
                  //           child: RegistrationPage(),
                  //         ),
                  //   ),
                  // );
                },
                child: Container(
                  padding: EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    color: AppColors.containerGreyColor,
                    border: Border.all(color: AppColors.borderGrey),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(CupertinoIcons.person_2, size: 20),
                ),
              ),
              SizedBox(width: 10),
              CircleAvatar(radius: 15, child: Text(userEmailInitial)),
            ],
          );
        },
      ),
      bottom: PreferredSize(
        child:
            size > 768
                ? Container(color: AppColors.borderGrey, height: 2)
                : SizedBox(),
        preferredSize: Size.fromHeight(4.0),
      ),
    );
  }
}
