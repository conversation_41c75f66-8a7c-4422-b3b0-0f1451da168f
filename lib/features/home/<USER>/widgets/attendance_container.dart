import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:flutter/material.dart';

class AttendanceContainer extends StatelessWidget {
  AttendanceContainer({super.key, required this.isMobile});
  final bool isMobile;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(isMobile ? 20 : 40),
      // constraints: BoxConstraints(maxWidth: 390),
      // width: 390,
      height: isMobile ? null : 240,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.secondary],
        ),
        borderRadius: BorderRadius.circular(13),
      ),
      child:
          isMobile
              ? Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                // mainAxisSize: MainAxisSize.min,
                children: [
                  attendanceText(),
                  SizedBox(height: 10),
                  Row(
                    children: [
                      attendanceBtn("PUNCH IN", () {}),
                      SizedBox(width: 10),
                      attendanceBtn("PUNCH OUT", () {}),
                    ],
                  ),
                ],
              )
              : Row(
                children: [
                  Expanded(flex: 2, child: attendanceText()),
                  SizedBox(width: 10),

                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(children: [attendanceBtn("PUNCH IN", () {})]),
                        SizedBox(height: 10),

                        Row(children: [attendanceBtn("PUNCH OUT", () {})]),
                      ],
                    ),
                  ),
                ],
              ),
    );
  }

  Expanded attendanceBtn(String text, Function onClick) {
    return Expanded(
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: isMobile ? null : EdgeInsets.symmetric(vertical: 20),
          backgroundColor: AppColors.black,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          textStyle: AppTextStyles.button,
        ),
        onPressed: () {
          onClick();
        },
        child: Text(text),
      ),
    );
  }

  Column attendanceText() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Attendance",
          style: TextStyle(color: AppColors.white, fontSize: 16),
        ),
        Text(
          DateTime.now().goodDayDate(),
          style: TextStyle(
            color: AppColors.white,
            fontSize: isMobile ? 26 : 48,
          ),
        ),
        SizedBox(height: 20),
        Text(
          DateTime.now().goodTime(),
          style: TextStyle(color: AppColors.white, fontSize: 16),
        ),
      ],
    );
  }
}
