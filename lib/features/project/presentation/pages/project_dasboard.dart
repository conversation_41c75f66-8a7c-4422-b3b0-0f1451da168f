import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/project/presentation/widgets/desktop_projectlayout.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../widgets/mobile_projectlayout.dart';

class ProjectDetailsPage extends StatelessWidget {
  final String projectId;
  final Widget child;
  // final String initialTab;

  const ProjectDetailsPage({
    required this.projectId,
    required this.child,
    //  required this.initialTab
  });

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UserCubit, UserState>(
      listener: (context, state) {
        // TODO: implement listener
      },
      builder: (context, state) {
        return ResponsiveWid(
          desktop: DesktopProjectLayout(projectId: projectId, child: child),
          mobile: MobileProjectLayout(projectId: projectId),
        );
      },
    );
  }
}
