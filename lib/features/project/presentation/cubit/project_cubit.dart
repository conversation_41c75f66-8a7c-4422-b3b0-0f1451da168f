import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/repo/project_repo.dart';
import 'package:meta/meta.dart';

part 'project_state.dart';

class ProjectCubit extends Cubit<ProjectState> {
  final ProjectRepo repo;
  ProjectCubit(this.repo) : super(ProjectState.initial());
  StreamSubscription<List<ProjectModel>>? projectsStream;

  void fetchProjects() {
    emit(state.copyWith(isLoading: true));

    projectsStream?.cancel();

    projectsStream = repo.getAllProjects().listen(
      (projects) {
        emit(state.copyWith(projects: projects, isLoading: false, message: ""));
      },
      onError: (error) {
        print(error.toString());
        emit(state.copyWith(isLoading: false, message: error.toString()));
      },
    );
  }

  deleteProject(String projectId) async {
    emit(state.copyWith(isLoading: true));

    await repo.deleteProject(projectId);
    emit(
      state.copyWith(isLoading: false, message: "Project deleted succesfully."),
    );
  }

  void fetchProjectById(String id) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      final project = await repo.getProjectById(id);
      if (project != null) {
        emit(
          state.copyWith(projectDetail: project, isLoading: false, message: ""),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          message: "Failed to load project: ${e.toString()}",
          isLoading: false,
        ),
      );
    }
  }
}
