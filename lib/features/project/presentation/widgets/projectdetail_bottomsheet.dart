import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ProjectDetailBottomsheet extends StatelessWidget {
  const ProjectDetailBottomsheet({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProjectCubit, ProjectState>(
      builder: (context, state) {
        if (state.projectDetail != null) {
          final ProjectModel? project = state.projectDetail;
          num totalAmount = project?.totalAmount ?? 0;
          num transactionsTotal = project!.transcations!.fold(
            0,
            (sum, t) => sum + t.amount,
          );
          num per = transactionsTotal / totalAmount;
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: getColorFromInput(
                          state.projectDetail?.projectTitle[0] ?? "",
                        ),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      height: 30,
                      width: 30,
                      child: Center(
                        child: Text(
                          state.projectDetail?.projectTitle[0].toUpperCase() ??
                              "",
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                    ),
                    SizedBox(width: 20),
                    Text(
                      state.projectDetail?.projectTitle ?? "",
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 30),
                detailRow("Status", project.projectStatus),
                SizedBox(height: 30),
                detailRow("Start date", project.createdAt?.goodDayDate()),
                SizedBox(height: 30),
                detailRow(
                  "Completion date(Estimated)",
                  project.completedAt?.goodDayDate(),
                ),
                SizedBox(height: 30),
                detailRow("Client name", project.clientName),
                SizedBox(height: 30),
                detailRow("Contact", project.clientContact),
                SizedBox(height: 30),
                Row(
                  children: [
                    Text(
                      "Payment",
                      style: TextStyle(color: AppColors.grey2, fontSize: 14),
                    ),
                    Spacer(),
                    Text("${(per * 100).toString()}%"),
                  ],
                ),
                SizedBox(height: 10),
                Row(
                  children: [
                    Expanded(
                      child: Stack(
                        children: <Widget>[
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.borderGrey,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            width: MediaQuery.of(context).size.width,
                            height: 8,
                          ),
                          Container(
                            decoration: BoxDecoration(
                              color: AppColors.secondary,
                              borderRadius: BorderRadius.circular(10),
                            ),

                            width:
                                (MediaQuery.of(context).size.width *
                                    per), // here you can define your percentage of progress, 0.2 = 20%, 0.3 = 30 % .....
                            height: 8,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10),
              ],
            ),
          );
        } else if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else {
          return Center(child: Text("Project detail not found"));
        }
      },
    );
  }

  Row detailRow(String title, String? value) {
    return Row(
      children: [
        Text(title, style: TextStyle(color: AppColors.grey2, fontSize: 14)),
        Spacer(),
        Text(value ?? "", style: TextStyle(fontWeight: FontWeight.w600)),
      ],
    );
  }
}
