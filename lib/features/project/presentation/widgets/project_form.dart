import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfield.dart';
import 'package:cp_associates/features/project/domain/entity/project_model.dart';
import 'package:cp_associates/features/project/domain/entity/transcation_model.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_form_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class ProjectForm extends StatefulWidget {
  ProjectModel? editProject;
  ProjectForm({Key? key, required this.editProject}) : super(key: key);

  @override
  State<ProjectForm> createState() => _ProjectFormState();
}

class _ProjectFormState extends State<ProjectForm> {
  final controller = MultiSelectController<String>();
  @override
  void initState() {
    context.read<ProjectFormCubit>().initializeForm(widget.editProject);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ProjectFormCubit, ProjectFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final projectFormCubit = context.read<ProjectFormCubit>();
        return SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Form(
              key: projectFormCubit.formKey,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          IconButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            icon: Icon(CupertinoIcons.xmark),
                          ),
                          Text("Add Project", style: AppTextStyles.heading2),
                          Spacer(),
                          PrimaryButton(
                            isLoading: state.isLoading,
                            text: "Save",
                            onPressed: () {
                              projectFormCubit.submit(
                                widget.editProject,
                                context,
                              );
                            },

                            width: 100,
                            height: 36,
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      CustomTextField(
                        controller: projectFormCubit.titleController,
                        hintText: "project title",
                        title: "Title",
                      ),
                      SizedBox(height: 10),
                      CustomTextField(
                        controller: projectFormCubit.descController,
                        hintText: "project description",
                        title: "Description",
                      ),
                      SizedBox(height: 10),
                      CustomTextField(
                        controller: projectFormCubit.addressController,
                        hintText: "project address",
                        title: "Address",
                      ),

                      SizedBox(height: 10),
                      Text("Assign Users"),
                      SizedBox(height: 10),
                      BlocBuilder<UserCubit, UserState>(
                        builder: (context, state) {
                          if (state.users.isNotEmpty) {
                            final users = state.users;

                            return MultiDropdown<String>(
                              items: List.generate(users.length, (index) {
                                return DropdownItem(
                                  label: users[index].name,
                                  value: users[index].docId,
                                  selected: projectFormCubit
                                      .state
                                      .selectedAssignUser
                                      .contains(users[index].docId),
                                  // selected:
                                );
                              }),
                              controller: controller,
                              enabled: true,
                              chipDecoration: const ChipDecoration(
                                backgroundColor: AppColors.secondary,
                                labelStyle: TextStyle(color: AppColors.white),
                                wrap: true,
                                runSpacing: 10,
                                spacing: 10,
                              ),
                              fieldDecoration: FieldDecoration(
                                // padding: EdgeInsets.all(1),
                                hintText: 'assign to',
                                hintStyle: const TextStyle(
                                  color: Colors.black87,
                                ),
                                prefixIcon: const Icon(CupertinoIcons.person_2),
                                showClearIcon: false,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(5),
                                  borderSide: const BorderSide(
                                    color: Colors.grey,
                                  ),
                                ),
                              ),
                              dropdownDecoration: const DropdownDecoration(
                                marginTop: 2,
                                maxHeight: 500,
                              ),
                              dropdownItemDecoration: DropdownItemDecoration(
                                selectedIcon: const Icon(
                                  Icons.check_box,
                                  color: Colors.green,
                                ),
                                disabledIcon: Icon(
                                  Icons.lock,
                                  color: Colors.grey.shade300,
                                ),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please select a assign user';
                                }
                                return null;
                              },
                              onSelectionChange: (selectedItems) {
                                projectFormCubit.updateSelectedUser(
                                  selectedItems,
                                );
                              },
                            );
                          }
                          return SizedBox();
                        },
                      ),

                      SizedBox(height: 10),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("Status"),
                          SizedBox(height: 5),
                          DropdownButtonHideUnderline(
                            child: DropdownButtonFormField(
                              value: state.projectStatus,
                              validator:
                                  (value) =>
                                      value == null || value.isEmpty
                                          ? 'Enter Project Status'
                                          : null,
                              decoration: InputDecoration(
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: AppColors.grey2,
                                  ),
                                ),
                                hintText: "project status",
                                hintStyle: AppTextStyles.hintText,

                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              items: const [
                                DropdownMenuItem(
                                  value: ProjectStatus.active,
                                  child: Text(ProjectStatus.active),
                                ),
                                DropdownMenuItem(
                                  value: ProjectStatus.finished,
                                  child: Text(ProjectStatus.finished),
                                ),
                                DropdownMenuItem(
                                  value: ProjectStatus.onHold,
                                  child: Text(ProjectStatus.onHold),
                                ),
                              ],
                              onChanged: (value) {
                                projectFormCubit.selectStatus(value ?? "");
                                // projectStatus = value;
                                //  projectStatusUpdateAt = DateTime.now();
                              },
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text("Completed At"),
                          SizedBox(height: 5),
                          TextFormField(
                            decoration: InputDecoration(
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey2),
                              ),
                              hintText:
                                  state.completedAt?.goodDayDate() ??
                                  "project complete date",
                              hintStyle: AppTextStyles.hintText,

                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              suffixIcon: IconButton(
                                onPressed: () async {
                                  projectFormCubit.selectCompletedDate(context);
                                },
                                icon: Icon(CupertinoIcons.calendar),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      CustomTextField(
                        controller: projectFormCubit.clientNameController,
                        hintText: "project client name",
                        title: "Client Name",
                      ),
                      SizedBox(height: 10),
                      CustomNumTextField(
                        controller: projectFormCubit.clientContactController,
                        hintText: "project client contact",
                        title: "Client Contact",
                      ),
                      SizedBox(height: 10),
                      CustomNumTextField(
                        controller: projectFormCubit.totalAmountController,
                        hintText: "project total amount",
                        title: "Total Amount",
                      ),
                      SizedBox(height: 10),
                      Row(
                        children: [
                          Text("Transcations"),
                          Spacer(),
                          IconButton(
                            onPressed: () {
                              state.transcations.add(
                                TranscationModel(
                                  createdAt: DateTime.now(),
                                  amount: 0,
                                ),
                              );
                              setState(() {});
                            },
                            icon: Icon(CupertinoIcons.add),
                          ),
                        ],
                      ),
                      state.transcations.isNotEmpty
                          ? Column(
                            children: [
                              ...List.generate(state.transcations.length, (
                                index,
                              ) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 10),
                                  child: TextFormField(
                                    decoration: InputDecoration(
                                      enabledBorder: OutlineInputBorder(
                                        borderSide: BorderSide(
                                          color: Colors.grey,
                                        ), // Use your AppColors.grey2
                                      ),
                                      hintText: "add amount",
                                      hintStyle: TextStyle(
                                        color: Colors.grey,
                                      ), // Use your AppTextStyles.label
                                      border: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      suffixIcon: IconButton(
                                        onPressed: () {
                                          state.transcations.removeAt(index);
                                        },
                                        icon: Icon(CupertinoIcons.delete),
                                      ),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Enter amount';
                                      }
                                      if (double.tryParse(value) == null) {
                                        return 'Enter a valid number';
                                      }
                                      return null;
                                    },
                                    controller: TextEditingController(
                                      text:
                                          state.transcations[index].amount
                                              .toString(),
                                    ),
                                    onChanged: (value) {
                                      state.transcations[index].amount =
                                          num.tryParse(value) ?? 0;
                                    },
                                    keyboardType: TextInputType.number,
                                  ),
                                );
                              }),
                            ],
                          )
                          : SizedBox(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
