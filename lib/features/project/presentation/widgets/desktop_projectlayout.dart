import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/features/activity/presentation/pages/activity_page.dart';
import 'package:cp_associates/features/bill/presentation/pages/bill_page.dart';
import 'package:cp_associates/features/document/presentation/pages/document_page.dart';
import 'package:cp_associates/features/project/presentation/cubit/project_cubit.dart';
import 'package:cp_associates/features/project/presentation/widgets/projectdetail_bottomsheet.dart';
import 'package:cp_associates/features/project/presentation/widgets/sidedrawer.dart';
import 'package:cp_associates/features/task/presentation/pages/task_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DesktopProjectLayout extends StatefulWidget {
  DesktopProjectLayout({
    super.key,
    required this.projectId,
    required this.child,
  });
  final String projectId;
  final Widget child;

  @override
  State<DesktopProjectLayout> createState() => _DesktopProjectLayoutState();
}

class _DesktopProjectLayoutState extends State<DesktopProjectLayout> {
  void initState() {
    super.initState();

    context.read<ProjectCubit>().fetchProjectById(widget.projectId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 252, 250, 250),
      // backgroundColor: Colors.grey.shade900,
      body: Row(
        // mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SideDrawer(projectId: widget.projectId),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(
                top: 100,
                left: 50,
                bottom: 50,
                // vertical: 100,
                // horizontal: 30,
              ),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.borderGrey),
                ),

                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: widget.child,
              ),
            ),
          ),

          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(
                top: 100,
                left: 30,
                right: 80,
                // vertical: 100,
                // horizontal: 30,
              ),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.borderGrey),
                  color: AppColors.containerGreyColor,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [ProjectDetailBottomsheet()],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
