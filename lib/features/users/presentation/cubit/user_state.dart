part of 'user_cubit.dart';

@immutable
class UserState {
  final List<UserModel> users;
  final String message;
  final bool isLoading;

  UserState({
    required this.users,
    required this.message,
    required this.isLoading,
  });

  factory UserState.initial() {
    return UserState(users: [], message: '', isLoading: false);
  }

  UserState copyWith({
    List<UserModel>? users,
    String? message,
    bool? isLoading,
    dynamic selectedFile,
    String? selectedType,
  }) {
    return UserState(
      users: users ?? this.users,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// final class UserInitial extends UserState {}

// final class UserLoading extends UserState {}

// final class UserListLoaded extends UserState {
//   final List<UserModel>? users;
//   UserListLoaded({this.users});
// }

// class UserFailure extends UserState {
//   final String? msg;
//   UserFailure({this.msg});
// }

// class UserUpdated extends UserState {}

// class UserDeleted extends UserState {}
