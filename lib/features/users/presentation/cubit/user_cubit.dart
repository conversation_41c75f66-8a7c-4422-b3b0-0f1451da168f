import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/users/domain/repo/user_repo.dart';
import 'package:meta/meta.dart';

part 'user_state.dart';

class UserCubit extends Cubit<UserState> {
  final UserRepo repo;
  StreamSubscription<List<UserModel>>? userStream;
  UserCubit(this.repo) : super(UserState.initial());

  void fetchAllUsers() {
    emit(state.copyWith(isLoading: true));
    userStream?.cancel();

    userStream = repo.getAllUsers().listen(
      (users) {
        emit(state.copyWith(users: users));
      },
      onError: (e) {
        emit(state.copyWith(message: "Failed to load Users: ${e.toString()}"));
      },
    );
  }

  UserModel? getUserById(String userId) {
    try {
      return state.users.firstWhere((user) => user.docId == userId);
    } catch (_) {
      return null; // Return null if user not found
    }
  }

  // UserModel getCurrentUser() {
  //   return FBAuth.auth.currentUser;
  // }
}
