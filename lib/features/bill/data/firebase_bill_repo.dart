import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/domain/repo/bill_repo.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/domain/repo/document_repo.dart';

class FbBillRepo implements BillRepo {
  final billRef = FBFireStore.bills;

  @override
  Future<void> deletebill(String billId) async {
    await billRef.doc(billId).delete();
  }

  @override
  Stream<List<BillModel>> getAllProjectBills(String projectId) {
    return billRef.where('projectId', isEqualTo: projectId).snapshots().map((
      snapshot,
    ) {
      return snapshot.docs.map((doc) => BillModel.fromSnapshot(doc)).toList();
    });
  }

  @override
  Future<void> updateBill(BillModel bill) async {
    await billRef.doc(bill.docId).update(bill.toJson());
  }

  @override
  Future<void> uploadBill(BillModel bill) async {
    final billRefrence = billRef.doc();
    final newDocument = bill.copyWith(docId: billRef.id);
    await billRefrence.set(newDocument.toJson());
  }
}
