import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/domain/repo/bill_repo.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
part 'bill_state.dart';

class BillCubit extends Cubit<BillState> {
  final BillRepo repo;
  StreamSubscription<List<BillModel>>? billsStream;
  BillCubit({required this.repo}) : super(BillState.initial());

  void fetchBills(String projectId) {
    emit(state.copyWith(isLoading: true, message: ""));

    billsStream?.cancel();

    billsStream = repo
        .getAllProjectBills(projectId)
        .listen(
          (bills) {
            emit(state.copyWith(bills: bills, isLoading: false, message: ""));
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch bills: ${error.toString()}",
              ),
            );
          },
        );
  }

  void filterBillsByType(String type) {
    if (type == docTypes.All) {
      emit(state.copyWith(bills: state.bills, selectedType: docTypes.All));
    } else if (type == docTypes.Images) {
      emit(state.copyWith(selectedType: type, message: ""));
    } else if (type == docTypes.Others) {
      emit(state.copyWith(selectedType: type, message: ""));
    } else {
      emit(state.copyWith(selectedType: type, message: ""));
    }
  }

  void deleteBill(String docId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await repo.deletebill(docId);
      emit(
        state.copyWith(isLoading: false, message: "Bill deleted successfully"),
      );
      //  fetchProjects();
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to delete bill: ${e.toString()}",
        ),
      );
    }
  }
}
