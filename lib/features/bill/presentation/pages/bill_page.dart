import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/presentation/cubit/bill_cubit.dart';
import 'package:cp_associates/features/bill/presentation/cubit/billform_cubit.dart';
import 'package:cp_associates/features/bill/presentation/widgets/bill_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BillPage extends StatefulWidget {
  final String projectId;
  BillPage({super.key, required this.projectId});

  @override
  State<BillPage> createState() => _BillPageState();
}

class _BillPageState extends State<BillPage> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BillCubit, BillState>(
      listener: (context, state) {
        // print();
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return ResponsiveWid(
          mobile: Stack(
            fit: StackFit.expand,
            // crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Align(
                alignment: Alignment.topCenter,
                child: Row(
                  children: [
                    FilterContainer(
                      title: "All",
                      onFilterTap: () {
                        context.read<BillCubit>().filterBillsByType(
                          docTypes.All,
                        );
                      },
                      isSelected: state.selectedType == docTypes.All,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "PDF",
                      onFilterTap: () {
                        context.read<BillCubit>().filterBillsByType(
                          docTypes.PDF,
                        );
                      },
                      isSelected: state.selectedType == docTypes.PDF,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "Images",
                      onFilterTap: () {
                        context.read<BillCubit>().filterBillsByType(
                          docTypes.Images,
                        );
                      },
                      isSelected: state.selectedType == docTypes.Images,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "Others",
                      onFilterTap: () {
                        context.read<BillCubit>().filterBillsByType(
                          docTypes.Others,
                        );
                      },
                      isSelected: state.selectedType == docTypes.Others,
                    ),
                  ],
                ),
              ),

              Padding(
                padding: const EdgeInsets.only(top: 50, bottom: 75),
                child: BillDetailTile(projectId: widget.projectId),
              ),

              Padding(
                padding: const EdgeInsets.only(bottom: 100),
                child: Align(
                  alignment: Alignment.bottomRight,
                  child: Container(
                    alignment: Alignment.bottomRight,
                    child: AddBtn(
                      text: "upload",

                      onPressed: () {
                        kIsWeb
                            ? showDialog(
                              context: context,
                              builder: (context) {
                                return Dialog(
                                  child: Container(
                                    width: 500,
                                    child: Padding(
                                      padding: const EdgeInsets.all(20),
                                      child: BlocProvider(
                                        create:
                                            (context) => BillFormCubit(
                                              context.read<BillCubit>().repo,
                                            ),
                                        child: BillForm(
                                          projectId: widget.projectId,
                                          editBill: null,
                                        ),
                                      ),
                                    ),
                                  ),
                                );
                              },
                            )
                            : showModalBottomSheet(
                              context: context,
                              builder: (context) {
                                return Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: BlocProvider(
                                    create:
                                        (context) => BillFormCubit(
                                          context.read<BillCubit>().repo,
                                        ),
                                    child: BillForm(
                                      projectId: widget.projectId,
                                      editBill: null,
                                    ),
                                  ),
                                );
                              },
                            );
                      },
                      color: Color(0xff2BAC76),
                    ),
                  ),
                ),
              ),
            ],
          ),
          desktop: Text("Loading"),
        );
      },
    );
  }
}

class BillDetailTile extends StatefulWidget {
  String projectId;

  BillDetailTile({super.key, required this.projectId});

  @override
  State<BillDetailTile> createState() => _BillDetailTileState();
}

class _BillDetailTileState extends State<BillDetailTile> {
  void initState() {
    context.read<BillCubit>().fetchBills(widget.projectId);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BillCubit, BillState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.bills.isNotEmpty) {
          List<BillModel> filteredBills = [];
          if (state.selectedType == docTypes.All) {
            filteredBills = state.bills;
          } else if (state.selectedType == docTypes.PDF) {
            filteredBills =
                state.bills
                    .where(
                      (bill) =>
                          bill.billExtenstion.toLowerCase() ==
                          docTypes.PDF.toLowerCase(),
                    )
                    .toList();
          } else if (state.selectedType == docTypes.Images) {
            filteredBills =
                state.bills
                    .where(
                      (bill) => imageExtensions.contains(
                        bill.billExtenstion.toLowerCase(),
                      ),
                    )
                    .toList();
          } else {
            filteredBills =
                state.bills
                    .where(
                      (bill) =>
                          !imageExtensions.contains(
                            bill.billExtenstion.toLowerCase(),
                          ) &&
                          bill.billExtenstion.toLowerCase() !=
                              docTypes.PDF.toLowerCase(),
                    )
                    .toList();
          }
          return filteredBills.isEmpty
              ? Center(child: Text("No Bill Found"))
              : SingleChildScrollView(
                child: Column(
                  spacing: 15,
                  children: [
                    ...List.generate(filteredBills.length, (index) {
                      final BillModel bill = filteredBills[index];
                      return Padding(
                        padding: EdgeInsets.only(
                          bottom: state.bills.length - 1 == index ? 50 : 0,
                        ),
                        child: GestureDetector(
                          onTap: () {
                            // context.read<BillFormCubit>().viewUploadBill(
                            //   context,
                            //   bill,
                            //   false,
                            // );
                          },
                          onDoubleTap: () async {
                            showModalBottomSheet(
                              context: context,
                              builder: (context) {
                                return Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: BlocProvider(
                                    create:
                                        (context) => BillFormCubit(
                                          context.read<BillCubit>().repo,
                                        ),
                                    child: BillForm(
                                      projectId: widget.projectId,
                                      editBill: bill,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          onLongPress: () {
                            showConfirmDeletDialog(context, () {
                              context.read<BillCubit>().deleteBill(bill.docId);
                            });
                          },
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.chipGreyColor,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: IconButton(
                                  onPressed: () {},
                                  icon: Icon(CupertinoIcons.doc, size: 24),
                                ),
                              ),
                              SizedBox(width: 15),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    bill.billName,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                  SizedBox(height: 5),
                                  Text(
                                    truncateText(bill.billDesc, 50),
                                    style: TextStyle(color: AppColors.grey2),
                                  ),
                                ],
                              ),
                              Spacer(),
                              Text(
                                bill.uploadAt.goodDayDate(),
                                style: TextStyle(color: AppColors.grey2),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              );
        } else if (state.bills.isEmpty) {
          return Center(child: Text("No Bill Found"));
        } else if (state.message.isNotEmpty) {
          return Center(child: Text(state.message.toString()));
        } else {
          return Center();
        }
      },
    );
  }
}
