import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfield.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/bill/presentation/cubit/billform_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class BillForm extends StatefulWidget {
  BillForm({super.key, required this.projectId, this.editBill});
  final BillModel? editBill;
  final String projectId;

  @override
  State<BillForm> createState() => _BillFormState();
}

class _BillFormState extends State<BillForm> {
  void initState() {
    super.initState();
    context.read<BillFormCubit>().initializeForm(widget.editBill);
  }

  @override
  Widget build(BuildContext context) {
    final billFormCubit = context.read<BillFormCubit>();

    return BlocConsumer<BillFormCubit, BillFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return Form(
          key: billFormCubit.formKey,
          child: StaggeredGrid.extent(
            maxCrossAxisExtent: 530,
            mainAxisSpacing: 15,
            crossAxisSpacing: 30,
            children: [
              ...List.generate(1, (index) {
                return SingleChildScrollView(
                  scrollDirection: Axis.vertical,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          IconButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            icon: Icon(CupertinoIcons.xmark),
                          ),
                          Text(
                            widget.editBill != null ? "Edit Bill" : "Add Bill",
                            style: AppTextStyles.heading2,
                          ),
                          Spacer(),
                          PrimaryButton(
                            isLoading: state.isLoading,
                            text: widget.editBill != null ? "Update" : "Save",
                            onPressed: () {
                              billFormCubit.submit(
                                editBill: widget.editBill,
                                projectId: widget.projectId,
                                context: context,
                              );
                            },

                            height: 36,
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      CustomTextField(
                        controller: billFormCubit.nameController,
                        hintText: "Bill Name *",
                        title: "",
                      ),
                      SizedBox(height: 10),
                      CustomNumTextField(
                        controller: billFormCubit.amountController,
                        hintText: "Bill Amount *",
                        title: "",
                      ),
                      SizedBox(height: 10),
                      CustomTextField(
                        controller: billFormCubit.descController,
                        hintText: "Bill Description *",
                        title: "",
                        // maxLines: 3,
                      ),
                      SizedBox(height: 10),
                      buildFilePreview(
                        context: context,
                        selectedFile: state.selectedFile,
                        dbFile: state.dbFile,
                        dbFileExt: widget.editBill?.billExtenstion,
                        dbFileName: widget.editBill?.billFileName,
                        isEdit: true,
                        onDelete: () {
                          final isDbFile =
                              state.selectedFile == null &&
                              state.dbFile != null;
                          context.read<BillFormCubit>().deletPickFile(isDbFile);
                        },
                        onView: () {
                          context.read<BillFormCubit>().viewPickFile(
                            state.dbFile,
                            context,
                            widget.editBill?.billExtenstion,
                          );
                        },
                      ),
                      SizedBox(height: 10),
                      TextFormField(
                        onTap: () async {
                          billFormCubit.pickFile(context);
                        },
                        readOnly: true,
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey),
                            borderRadius: BorderRadius.circular(
                              10,
                            ), // Use your AppColors.grey2
                          ),
                          hintText: "Upload Bill(image,pdf) *",
                          prefixIcon: Icon(CupertinoIcons.up_arrow),
                          hintStyle: TextStyle(
                            color: Colors.grey,
                          ), // Use your AppTextStyles.label
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                      SizedBox(height: 10),
                      GestureDetector(
                        onTap: () {
                          billFormCubit.pickFileFromCamera(context);
                        },
                        child: AbsorbPointer(
                          child: TextFormField(
                            readOnly: true,
                            decoration: InputDecoration(
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Colors.grey,
                                ), // Use your AppColors.grey2
                                borderRadius: BorderRadius.circular(10),
                              ),
                              hintText: "Take new photo",
                              prefixIcon: Icon(CupertinoIcons.camera),
                              hintStyle: TextStyle(
                                color: Colors.grey,
                              ), // Use your AppTextStyles.label
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        );
      },
    );
  }
}
