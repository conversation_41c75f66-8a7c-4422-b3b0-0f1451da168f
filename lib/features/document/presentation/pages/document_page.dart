import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_cubit.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_state.dart';
import 'package:cp_associates/features/document/presentation/cubit/documentform_cubit.dart';
import 'package:cp_associates/features/document/presentation/widgets/document_form.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class DocumentPage extends StatefulWidget {
  final String projectId;
  DocumentPage({super.key, required this.projectId});

  @override
  State<DocumentPage> createState() => _DocumentPageState();
}

class _DocumentPageState extends State<DocumentPage> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DocumentCubit, DocumentState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        return ResponsiveWid(
          mobile: Column(
            crossAxisAlignment: CrossAxisAlignment.start,

            children: [
              Row(
                children: [
                  FilterContainer(
                    title: "All",
                    onFilterTap: () {
                      context.read<DocumentCubit>().filterDocumentsByType(
                        docTypes.All,
                      );
                    },
                    isSelected: state.selectedType == docTypes.All,
                  ),
                  SizedBox(width: 10),
                  FilterContainer(
                    title: "PDF",
                    onFilterTap: () {
                      context.read<DocumentCubit>().filterDocumentsByType(
                        docTypes.PDF,
                      );
                    },
                    isSelected: state.selectedType == docTypes.PDF,
                  ),
                  SizedBox(width: 10),
                  FilterContainer(
                    title: "Images",
                    onFilterTap: () {
                      context.read<DocumentCubit>().filterDocumentsByType(
                        docTypes.Images,
                      );
                    },
                    isSelected: state.selectedType == docTypes.Images,
                  ),
                ],
              ),
              SizedBox(height: 20),
              DocumentDetailTile(projectId: widget.projectId),
              Spacer(),
              Container(
                alignment: Alignment.bottomRight,
                child: AddBtn(
                  text: "upload",
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) {
                        return Padding(
                          padding: const EdgeInsets.all(16),
                          child: BlocProvider(
                            create:
                                (context) => DocumentFormCubit(
                                  context.read<DocumentCubit>().documentRepo,
                                ),
                            child: DocumentForm(
                              projectId: widget.projectId,
                              editDocument: null,
                            ),
                          ),
                        );
                      },
                    );
                  },
                  color: Color(0xffD57A49),
                ),
              ),
              SizedBox(height: 100),
            ],
          ),
          desktop: Text("Loading"),
        );
      },
    );
  }
}

class DocumentDetailTile extends StatefulWidget {
  String projectId;
  DocumentDetailTile({super.key, required this.projectId});

  @override
  State<DocumentDetailTile> createState() => _DocumentDetailTileState();
}

class _DocumentDetailTileState extends State<DocumentDetailTile> {
  void initState() {
    context.read<DocumentCubit>().fetchDocuments(widget.projectId);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<DocumentCubit, DocumentState>(
      builder: (context, state) {
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.documents.isNotEmpty) {
          List<DocumentModel> filteredDocuments = [];
          if (state.selectedType == docTypes.All) {
            filteredDocuments = state.documents;
          } else if (state.selectedType == docTypes.PDF) {
            filteredDocuments =
                state.documents
                    .where(
                      (document) =>
                          document.docExtenstion.toLowerCase() ==
                          docTypes.PDF.toLowerCase(),
                    )
                    .toList();
          } else if (state.selectedType == docTypes.Images) {
            filteredDocuments =
                state.documents
                    .where(
                      (document) => imageExtensions.contains(
                        document.docExtenstion.toLowerCase(),
                      ),
                    )
                    .toList();
          }
          return filteredDocuments.isEmpty
              ? Center(child: Text("No Document found"))
              : Column(
                spacing: 15,
                children: [
                  ...List.generate(filteredDocuments.length, (index) {
                    final DocumentModel document = filteredDocuments[index];
                    return GestureDetector(
                      onDoubleTap: () {
                        showModalBottomSheet(
                          context: context,
                          builder: (context) {
                            return Padding(
                              padding: const EdgeInsets.all(16),
                              child: BlocProvider(
                                create:
                                    (context) => DocumentFormCubit(
                                      context
                                          .read<DocumentCubit>()
                                          .documentRepo,
                                    ),
                                child: DocumentForm(
                                  projectId: widget.projectId,
                                  editDocument: document,
                                ),
                              ),
                            );
                          },
                        );
                      },
                      onLongPress: () {
                        showConfirmDeletDialog(context, () {
                          context.read<DocumentCubit>().deleteDocument(
                            document.docId,
                          );
                          Navigator.of(context).pop();
                        });
                      },
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: AppColors.chipGreyColor,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: IconButton(
                              onPressed: () {},
                              icon: Icon(CupertinoIcons.doc, size: 24),
                            ),
                          ),
                          SizedBox(width: 15),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                document.documentName,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              SizedBox(height: 5),
                              Text(
                                document.uploadAt.goodDayDate(),
                                style: TextStyle(color: AppColors.grey2),
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  }),
                ],
              );
        } else if (state.documents.isEmpty) {
          return Center(child: Text("No Document found"));
        } else if (state.message.isNotEmpty) {
          return Center(child: Text(state.message.toString()));
        } else {
          return Center();
        }
      },
    );
  }
}
