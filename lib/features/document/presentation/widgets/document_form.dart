import 'package:cached_network_image/cached_network_image.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfield.dart';
import 'package:cp_associates/core/widgets/file_preview.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/presentation/cubit/documentform_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class DocumentForm extends StatefulWidget {
  final DocumentModel? editDocument;
  final String projectId;

  const DocumentForm({Key? key, required this.projectId, this.editDocument})
    : super(key: key);

  @override
  State<DocumentForm> createState() => _DocumentFormState();
}

class _DocumentFormState extends State<DocumentForm> {
  @override
  void initState() {
    super.initState();
    final documentFormCubit = context.read<DocumentFormCubit>();
    documentFormCubit.initializeForm(widget.editDocument);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<DocumentFormCubit, DocumentFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final documentFormCubit = context.read<DocumentFormCubit>();

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Form(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: Icon(CupertinoIcons.xmark),
                      ),
                      Text(
                        widget.editDocument != null
                            ? "Edit Document"
                            : "Add Document",
                        style: AppTextStyles.heading2,
                      ),
                      Spacer(),
                      PrimaryButton(
                        isLoading: state.isLoading,
                        text: widget.editDocument != null ? "Update" : "Save",
                        onPressed: () {
                          documentFormCubit.submit(
                            context: context,
                            projectId: widget.projectId,
                            editDocument: widget.editDocument,
                          );
                        },

                        height: 36,
                      ),
                    ],
                  ),
                  const SizedBox(height: 10),

                  CustomTextField(
                    controller: documentFormCubit.nameController,
                    hintText: "Document Name *",
                    title: "",
                  ),
                  const SizedBox(height: 10),

                  buildFilePreview(context, state),

                  const SizedBox(height: 10),

                  GestureDetector(
                    onTap: () async => documentFormCubit.pickFile(context),
                    child: AbsorbPointer(
                      child: TextFormField(
                        readOnly: true,
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          hintText: "Upload Document (image, pdf) *",
                          prefixIcon: Icon(CupertinoIcons.up_arrow),
                          hintStyle: TextStyle(color: Colors.grey),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Optionally: Take photo with camera
                  GestureDetector(
                    onTap: () {
                      // Optional: implement camera capture
                    },
                    child: AbsorbPointer(
                      child: TextFormField(
                        readOnly: true,
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          hintText: "Take new photo",
                          prefixIcon: Icon(CupertinoIcons.camera),
                          hintStyle: TextStyle(color: Colors.grey),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 10),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget buildFilePreview(BuildContext context, DocumentFormState state) {
    //upload file
    final file = state.selectedFile;
    final extension = file?.extension?.toLowerCase() ?? "";
    //db file
    final dbFile = state.dbFile;
    final dbExt = widget.editDocument?.docExtenstion;
    final dbName = widget.editDocument?.docFileName;
    void onTapHandler() {
      context.read<DocumentFormCubit>().viewPickFile(
        state.dbFile,
        context,
        widget.editDocument?.docExtenstion,
      );
    }

    return InkWell(
      onTap: () {
        widget.editDocument != null
            ? onTapHandler()
            : context.read<DocumentFormCubit>().viewPickFile(
              null,
              context,
              null,
            );
      },
      child:
          file != null
              ? checkPdfExtenstion(extension)
                  ? pdfPreviewConatiner(
                    selectedFile: file,
                    isEdit: true,
                    onDelete: () {
                      context.read<DocumentFormCubit>().deletPickFile(false);
                    },
                    onView: () {
                      context.read<DocumentFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editDocument?.docExtenstion,
                      );
                    },
                  )
                  : imagePreviewContainer(
                    selectedFile: file,
                    isEdit: true,
                    onDelete: () {
                      context.read<DocumentFormCubit>().deletPickFile(false);
                    },
                    onView: () {
                      context.read<DocumentFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editDocument?.docExtenstion,
                      );
                    },
                  )
              : dbFile != null
              ? checkPdfExtenstion(dbExt)
                  ? pdfPreviewConatiner(
                    dbFile: dbFile,
                    dbFileName: dbName,
                    dbFileExt: dbExt,
                    isEdit: true,
                    onDelete: () {
                      context.read<DocumentFormCubit>().deletPickFile(true);
                    },
                    onView: () {
                      context.read<DocumentFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editDocument?.docExtenstion,
                      );
                    },
                  )
                  : imagePreviewContainer(
                    dbFile: dbFile,
                    dbFileExt: dbExt,
                    isEdit: true,
                    onDelete: () {
                      context.read<DocumentFormCubit>().deletPickFile(true);
                    },
                    onView: () {
                      context.read<DocumentFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editDocument?.docExtenstion,
                      );
                    },
                  )
              // CachedNetworkImage(imageUrl: dbFile, width: 50, height: 50)
              : SizedBox(),
    );
  }

  Widget buildExistingDocumentPreview(BuildContext context, DocumentModel doc) {
    if (doc.docExtenstion.toLowerCase() == docTypes.PDF.toLowerCase()) {
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => Scaffold(
                    appBar: AppBar(title: Text("View PDF")),
                    body: SfPdfViewer.network(doc.documentUrl),
                  ),
            ),
          );
        },
        child: pdfPreviewConatiner(dbFile: "Document.pdf", isEdit: true),
      );
    } else {
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => Scaffold(
                    appBar: AppBar(title: Text("Image Preview")),
                    body: Center(
                      child: CachedNetworkImage(
                        imageUrl: doc.documentUrl,
                        placeholder: (_, __) => CircularProgressIndicator(),
                        errorWidget: (_, __, ___) => Icon(Icons.error),
                      ),
                    ),
                  ),
            ),
          );
        },
        child: CachedNetworkImage(
          imageUrl: doc.documentUrl,
          width: 50,
          height: 50,
          fit: BoxFit.cover,
        ),
      );
    }
  }

  // Widget getFile(
  //   SelectedImage? selectedFile,
  //   String? dbFile,
  //   String? dbExt,
  //   String? dbName,
  //   String extension,
  //   DocumentFormState state,
  // ) {
  //   {
  //     return selectedFile != null
  //         ? checkExtenstion(extension) == docTypes.PDF
  //             ? pdfPreviewConatiner(
  //               selectedFile: selectedFile,
  //               isEdit: true,
  //               onDelete: () {
  //                 context.read<DocumentFormCubit>().deletPickFile(false);
  //               },
  //               onView: () {
  //                 context.read<DocumentFormCubit>().viewPickFile(
  //                   state.dbFile,
  //                   context,
  //                   widget.editDocument?.docExtenstion,
  //                 );
  //               },
  //             )
  //             : checkExtenstion(extension) == docTypes.Images
  //             ? imagePreviewContainer(
  //               selectedFile: selectedFile,
  //               isEdit: true,
  //               onDelete: () {
  //                 context.read<DocumentFormCubit>().deletPickFile(false);
  //               },
  //               onView: () {
  //                 context.read<DocumentFormCubit>().viewPickFile(
  //                   state.dbFile,
  //                   context,
  //                   widget.editDocument?.docExtenstion,
  //                 );
  //               },
  //             )
  //             : OtherFilePreviewContainer(
  //               selectedFile: selectedFile,
  //               isEdit: true,
  //               onDelete: () {
  //                 context.read<DocumentFormCubit>().deletPickFile(false);
  //               },
  //               onView: () {
  //                 context.read<DocumentFormCubit>().viewPickFile(
  //                   state.dbFile,
  //                   context,
  //                   widget.editDocument?.docExtenstion,
  //                 );
  //               },
  //             )
  //         : dbFile != null
  //         ? checkPdfExtenstion(dbExt)
  //             ? pdfPreviewConatiner(
  //               dbFile: dbFile,
  //               dbFileName: dbName,
  //               dbFileExt: dbExt,
  //               isEdit: true,
  //               onDelete: () {
  //                 context.read<DocumentFormCubit>().deletPickFile(true);
  //               },
  //               onView: () {
  //                 context.read<DocumentFormCubit>().viewPickFile(
  //                   state.dbFile,
  //                   context,
  //                   widget.editDocument?.docExtenstion,
  //                 );
  //               },
  //             )
  //             : imagePreviewContainer(
  //               dbFile: dbFile,
  //               dbFileExt: dbExt,
  //               isEdit: true,
  //               onDelete: () {
  //                 context.read<DocumentFormCubit>().deletPickFile(true);
  //               },
  //               onView: () {
  //                 context.read<DocumentFormCubit>().viewPickFile(
  //                   state.dbFile,
  //                   context,
  //                   widget.editDocument?.docExtenstion,
  //                 );
  //               },
  //             )
  //         // CachedNetworkImage(imageUrl: dbFile, width: 50, height: 50)
  //         : SizedBox();
  //   }
  // }
}
