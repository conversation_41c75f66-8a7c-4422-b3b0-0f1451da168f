import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/domain/repo/document_repo.dart';
import 'package:cp_associates/features/document/presentation/cubit/document_state.dart';
import 'package:cp_associates/features/stroage/data/firebase_storage_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class DocumentCubit extends Cubit<DocumentState> {
  final DocumentRepo documentRepo;
  StreamSubscription<List<DocumentModel>>? documentStream;

  DocumentCubit({required this.documentRepo}) : super(DocumentState.initial());

  void deleteDocument(String docId) async {
    emit(state.copyWith(isLoading: true, message: ""));
    try {
      await documentRepo.deleteDocument(docId);
      emit(
        state.copyWith(
          isLoading: false,
          message: "Document deleted successfully",
        ),
      );
      //  fetchProjects();
    } catch (e) {
      emit(
        state.copyWith(
          isLoading: false,
          message: "Failed to delete Document: ${e.toString()}",
        ),
      );
    }
  }

  void filterDocumentsByType(String type) {
    if (type == docTypes.All) {
      emit(
        state.copyWith(
          documents: state.documents,
          selectedType: docTypes.All,
          isLoading: false,
          message: "",
        ),
      );
    } else if (type == docTypes.Images) {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    } else {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    }
  }

  void fetchDocuments(String projectId) {
    emit(state.copyWith(isLoading: true, message: ""));

    documentStream?.cancel();

    documentStream = documentRepo
        .getAllProjectDocument(projectId)
        .listen(
          (documents) {
            emit(
              state.copyWith(
                documents: documents,
                isLoading: false,
                message: "",
              ),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch bills: ${error.toString()}",
              ),
            );
          },
        );
  }

}
