import 'package:cp_associates/core/services/image_picker.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';

class DocumentState {
  final List<DocumentModel> documents;
  final String message;
  final bool isLoading;

  final String selectedType;

  DocumentState({
    required this.documents,
    required this.message,
    required this.isLoading,

    required this.selectedType,
  });

  factory DocumentState.initial() {
    return DocumentState(
      documents: [],
      message: '',
      isLoading: false,
      selectedType: 'All',
    );
  }

  DocumentState copyWith({
    List<DocumentModel>? documents,
    String? message,
    bool? isLoading,
    dynamic selectedFile,
    String? selectedType,
  }) {
    return DocumentState(
      documents: documents ?? this.documents,
      message: message ?? this.message,
      isLoading: isLoading ?? this.isLoading,
      selectedType: selectedType ?? this.selectedType,
    );
  }
}
