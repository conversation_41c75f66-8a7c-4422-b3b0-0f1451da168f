import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/document/domain/entity/document_model.dart';
import 'package:cp_associates/features/document/domain/repo/document_repo.dart';

class FirebaseDocumentRepo implements DocumentRepo {
  final documentRef = FBFireStore.documents;

  @override
  Future<void> uploadDocument(DocumentModel document) async {
    final docRef = documentRef.doc();
    final newDocument = document.copyWith(docId: docRef.id);
    await docRef.set(newDocument.toJson());
  }

  @override
  Future<void> updateDocument(DocumentModel document) async {
    await documentRef.doc(document.docId).update(document.toJson());
  }

  @override
  Future<void> deleteDocument(String documentId) async {
    await documentRef.doc(documentId).delete();
  }

  @override
  Stream<List<DocumentModel>> getAllProjectDocument(String projectId) {
    return documentRef.where('projectId', isEqualTo: projectId).snapshots().map(
      (snapshot) {
        return snapshot.docs
            .map((doc) => DocumentModel.fromSnapshot(doc))
            .toList();
      },
    );
  }
}
