import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/file_preview.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_form_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActivityForm extends StatefulWidget {
  final String projectId;
  final ActivityModel? editActivity;
  const ActivityForm({super.key, required this.projectId, this.editActivity});

  @override
  State<ActivityForm> createState() => _ActivityFormState();
}

class _ActivityFormState extends State<ActivityForm> {
  @override
  void initState() {
    // TODO: implement initState

    context.read<ActivityFormCubit>().initializeForm(widget.editActivity);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<ActivityFormCubit, ActivityFormState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        final activityFormCubit = context.read<ActivityFormCubit>();
        return Form(
          key: context.read<ActivityFormCubit>().formKey,
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      Text("Activity", style: TextStyle(fontSize: 20)),
                      Spacer(),
                      IconButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        icon: Icon(CupertinoIcons.xmark),
                      ),
                    ],
                  ),
                  SizedBox(height: 20),

                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            activityFormCubit.pickFile(context);
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              vertical: 60,
                              horizontal: 10,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(CupertinoIcons.up_arrow),
                                Text("Upload Document "),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 20),
                      Expanded(
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: 60,
                            horizontal: 10,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,

                            children: [
                              Icon(CupertinoIcons.up_arrow),
                              Text("Take New Photo"),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 30),
                  buildFilePreview(context, state),
                  SizedBox(height: 10),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: TextFormField(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Enter Message';
                            }
                            return null;
                          },
                          controller: activityFormCubit.messageController,
                          decoration: InputDecoration(
                            suffixStyle: TextStyle(
                              background: Paint()..color = AppColors.secondary,
                            ),
                            suffixIcon: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: InkWell(
                                onTap: () {
                                  context.read<ActivityFormCubit>().submit(
                                    context: context,
                                    projectId: widget.projectId,
                                    editActivity: null,
                                  );
                                },
                                child: Container(
                                  padding: EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    color: AppColors.secondary,
                                  ),
                                  child: Icon(
                                    CupertinoIcons.arrow_right,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            hintText: "Enter Message",
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 30),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget buildFilePreview(BuildContext context, ActivityFormState state) {
    //upload file
    final file = state.selectedFile;
    final extension = file?.extension?.toLowerCase() ?? "";
    //db file
    final dbFile = state.dbFile;
    final dbExt = widget.editActivity?.attachmentType;
    final dbName = widget.editActivity?.attachmentName;
    void onTapHandler() {
      context.read<ActivityFormCubit>().viewPickFile(
        state.dbFile,
        context,
        widget.editActivity?.attachmentType,
      );
    }

    return InkWell(
      onTap: () {
        widget.editActivity != null
            ? onTapHandler()
            : context.read<ActivityFormCubit>().viewPickFile(
              null,
              context,
              null,
            );
      },
      child:
          file != null
              ? checkPdfExtenstion(extension)
                  ? pdfPreviewConatiner(
                    selectedFile: file,
                    isEdit: true,
                    onDelete: () {
                      context.read<ActivityFormCubit>().deletPickFile(false);
                    },
                    onView: () {
                      context.read<ActivityFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editActivity?.attachmentType,
                      );
                    },
                  )
                  : imagePreviewContainer(
                    selectedFile: file,
                    isEdit: true,
                    onDelete: () {
                      context.read<ActivityFormCubit>().deletPickFile(false);
                    },
                    onView: () {
                      context.read<ActivityFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editActivity?.attachmentType,
                      );
                    },
                  )
              : dbFile != null
              ? checkPdfExtenstion(dbExt)
                  ? pdfPreviewConatiner(
                    dbFile: dbFile,
                    dbFileName: dbName,
                    dbFileExt: dbExt,
                    isEdit: true,
                  )
                  : imagePreviewContainer(
                    dbFile: dbFile,
                    dbFileExt: dbExt,
                    isEdit: true,
                  )
              // CachedNetworkImage(imageUrl: dbFile, width: 50, height: 50)
              : SizedBox(),
    );
  }
}
