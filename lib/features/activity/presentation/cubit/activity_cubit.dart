import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/domain/repo/activity_repo.dart';
import 'package:meta/meta.dart';

part 'activity_state.dart';

class ActivityCubit extends Cubit<ActivityState> {
  final ActivityRepo activityRepo;
  ActivityCubit(this.activityRepo) : super(ActivityState.initial());
  StreamSubscription<List<ActivityModel>>? activityStream;

  void fetchActivities(String projectId) {
    emit(state.copyWith(isLoading: true, message: ""));

    activityStream?.cancel();

    activityStream = activityRepo
        .fetchProjectActivity(projectId)
        .listen(
          (activities) {
            activities.sort((a, b) => a.sendAt.compareTo(b.sendAt));
            emit(
              state.copyWith(
                activities: activities,
                isLoading: false,
                message: "",
              ),
            );
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch activities: ${error.toString()}",
              ),
            );
          },
        );
  }
}
