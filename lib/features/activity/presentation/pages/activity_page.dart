import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/file_preview.dart';
import 'package:cp_associates/core/widgets/file_view.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/activity/domain/antity/activity_model.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_cubit.dart';
import 'package:cp_associates/features/activity/presentation/cubit/activity_form_cubit.dart';
import 'package:cp_associates/features/activity/presentation/widget/activity_form.dart';
import 'package:cp_associates/features/activity/presentation/widget/message_bubble_cliper.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

class ActivityPage extends StatelessWidget {
  ActivityPage({super.key, required this.projectId});
  final String? projectId;
  @override
  Widget build(BuildContext context) {
    context.read<ActivityCubit>().fetchActivities(projectId ?? "");
    return ResponsiveWid(
      mobile: Stack(
        fit: StackFit.expand,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 100),

            child: ActivityDetailTile(projectId: projectId ?? ""),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 100),
            child: Align(
              alignment: Alignment.bottomRight,
              child: Container(
                alignment: Alignment.bottomRight,
                child: AddBtn(
                  text: "acivity",
                  onPressed: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) {
                        return Padding(
                          padding: const EdgeInsets.all(16),
                          child: BlocProvider(
                            create:
                                (context) => ActivityFormCubit(
                                  context.read<ActivityCubit>().activityRepo,
                                ),
                            child: ActivityForm(projectId: projectId ?? ""),
                          ),
                        );
                      },
                    );
                  },
                  color: AppColors.secondary,
                ),
              ),
            ),
          ),
        ],
      ),
      desktop: Text("Activity"),
    );
  }
}

class ActivityDetailTile extends StatefulWidget {
  String projectId;
  ActivityDetailTile({super.key, required this.projectId});

  @override
  State<ActivityDetailTile> createState() => _ActivityDetailTileState();
}

class _ActivityDetailTileState extends State<ActivityDetailTile> {
  @override
  Widget build(BuildContext context) {
    final activityCubit = context.read<ActivityCubit>();
    DateTime? previousDate;
    return BlocConsumer<ActivityCubit, ActivityState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else if (state.activities.isEmpty) {
          return Center(child: Text("No Activity Avaliable"));
        } else {
          return SingleChildScrollView(
            reverse: true,
            physics: ClampingScrollPhysics(),
            dragStartBehavior: DragStartBehavior.down,
            child: Column(
              spacing: 10,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...List.generate(state.activities.length, (index) {
                  final activity = state.activities[index];
                  final isSender =
                      activity.senderId == FBAuth.auth.currentUser?.uid;

                  final currentDate = DateTime(
                    activity.sendAt.year,
                    activity.sendAt.month,
                    activity.sendAt.day,
                  );

                  bool showDateHeader = false;
                  if (previousDate == null || currentDate != previousDate) {
                    showDateHeader = true;
                    previousDate = currentDate;
                  }

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (showDateHeader)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                vertical: 5,
                                horizontal: 10,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.containerGreyColor,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(color: AppColors.borderGrey),
                              ),
                              child: Text(getDateLabel(currentDate)),
                            ),
                          ),
                        ),
                      Align(
                        alignment:
                            isSender
                                ? Alignment.centerRight
                                : Alignment.centerLeft,
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(
                                top: 10.0,
                                left: isSender ? 0 : 35,
                                right: isSender ? 35 : 0,
                                bottom:
                                    state.activities.length - 1 == index
                                        ? 50
                                        : 0,
                              ),
                              child: ClipPath(
                                clipper: MessageBubbleClipper(
                                  isSender: isSender,
                                ),
                                child: Container(
                                  padding: EdgeInsets.only(
                                    bottom: 10,
                                    left: isSender ? 10 : 20,
                                    right: isSender ? 20 : 10,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        isSender
                                            ? AppColors.msg
                                            : Colors.grey.shade200,
                                  ),
                                  child: messageContent(activity),
                                ),
                              ),
                            ),

                            Positioned(
                              top: 0,
                              left: isSender ? null : 0,
                              right: isSender ? 0 : null,
                              child: CircleAvatar(
                                radius: 15,
                                child: Text(
                                  context
                                          .read<UserCubit>()
                                          .getUserById(activity.senderId)
                                          ?.name[0]
                                          .toUpperCase() ??
                                      "",
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }),
              ],
            ),
          );
        }
      },
    );
  }

  Column messageContent(ActivityModel activity) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        activity.attachment != null
            ? Padding(
              padding: const EdgeInsets.only(top: 10.0),
              child: () {
                final fileType = checkExtenstion(activity.attachmentType);

                if (fileType == docTypes.Images) {
                  return InkWell(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => FileView(
                                dbFile: activity.attachment,
                                dbFileExt: activity.attachmentType,
                              ),
                        ),
                      );
                    },
                    child: CachedNetworkImage(
                      imageUrl: activity.attachment ?? "",
                      width: 250,
                      fit: BoxFit.cover,
                    ),
                  );
                } else if (fileType == docTypes.PDF) {
                  return pdfPreviewConatiner(
                    onView:
                        () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (_) => Scaffold(
                                  appBar: AppBar(title: Text("View PDF")),
                                  body: SfPdfViewer.network(
                                    activity.attachment ?? "",
                                  ),
                                ),
                          ),
                        ),
                    dbFile: activity.attachment,
                    dbFileExt: activity.attachmentType,
                    dbFileName: activity.attachmentName,
                    isEdit: false,
                  );
                } else if (fileType == docTypes.Others) {
                  return OtherFilePreviewContainer(
                    onView: () => activity.attachment,
                    dbFile: activity.attachment,
                    dbFileExt: activity.attachmentType,
                    dbFileName: activity.attachmentName,
                    isEdit: false,
                  );
                } else {
                  return Text("Unsupported file type");
                }
              }(),
            )
            : SizedBox(),
        const SizedBox(height: 10),
        (activity.message != null) ? Text(activity.message ?? "") : SizedBox(),
      ],
    );
  }

  Widget imagePreview(ActivityModel activity) {
    return CachedNetworkImage(
      imageUrl: activity.attachment ?? "",
      width: 250,
      fit: BoxFit.cover,
    );
  }
}
