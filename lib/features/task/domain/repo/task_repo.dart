import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/features/bill/domain/entity/bill_model.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';

abstract class TaskRepo {
  Future<String> createTask(TaskModel task);
  Future<void> updateTask(TaskModel task);
  Future<void> deleteTask(String taskId);
  Stream<List<TaskModel>> fetchProjectTask(String projectId);
  Stream<TaskModel> fetchTaskById(String taskId);
}
