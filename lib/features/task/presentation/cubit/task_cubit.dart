import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/domain/repo/task_repo.dart';

part 'task_state.dart';

class TaskCubit extends Cubit<TaskState> {
  TaskRepo taskRepo;
  TaskCubit({required this.taskRepo}) : super(TaskState.initial());

  StreamSubscription<List<TaskModel>>? taskStream;

  void filterTaskByType(String type) {
    emit(state.copyWith(isLoading: true, message: ""));
    if (type == TaskTypes.All) {
      emit(
        state.copyWith(
          task: state.task,
          selectedType: TaskTypes.All,
          isLoading: false,
          message: "",
        ),
      );
    } else if (type == TaskTypes.mytask) {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    } else if (type == TaskTypes.completed) {
      state.task.where((task) {
        return task.status.contains(TaskTypes.completed);
      }).toList();
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    } else {
      emit(state.copyWith(selectedType: type, isLoading: false, message: ""));
    }
  }

  fetchAllTask(String projectId) {
    emit(state.copyWith(isLoading: true, message: ""));
    taskStream = taskRepo
        .fetchProjectTask(projectId)
        .listen(
          (task) {
            emit(state.copyWith(task: task, isLoading: false, message: ""));
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch bills: ${error.toString()}",
              ),
            );
          },
        );
  }

  fetchTaskById(String taskId) async {
    emit(state.copyWith(isLoading: true));
    await taskRepo
        .fetchTaskById(taskId)
        .listen(
          (task) {
            emit(state.copyWith(isLoading: false, taskDetail: task));
          },
          onError: (error) {
            print(error.toString());
            emit(
              state.copyWith(
                isLoading: false,
                message: "Failed to fetch task: ${error.toString()}",
              ),
            );
          },
        );
  }

  deletTaskLog(String taskId) async {
    final taskLogs =
        await FBFireStore.tasks.doc(taskId).collection("tasklogs").get();

    for (final doc in taskLogs.docs) {
      await doc.reference.delete();
    }
  }

  deletTask(String taskId) async {
    emit(state.copyWith(isLoading: true));
    deletTaskLog(taskId);
    await taskRepo.deleteTask(taskId);
    emit(
      state.copyWith(isLoading: false, message: "Task deleted succesfully."),
    );
  }


}
