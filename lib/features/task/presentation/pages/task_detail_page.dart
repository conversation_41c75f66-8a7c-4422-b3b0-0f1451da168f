import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/file_preview.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/task/presentation/pages/task_page.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class TaskDetailPage extends StatefulWidget {
  String taskId;
  TaskDetailPage({super.key, required this.taskId});

  @override
  State<TaskDetailPage> createState() => _TaskDetailPageState();
}

class _TaskDetailPageState extends State<TaskDetailPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    context.read<TaskCubit>().fetchTaskById(widget.taskId);
  }

  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();

    return Scaffold(
      appBar: AppBar(),
      body: ResponsiveWid(
        mobile: BlocConsumer<TaskCubit, TaskState>(
          listener: (context, state) {
            if (state.message.isNotEmpty) {
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(state.message)));
            }
          },
          builder: (context, state) {
            if (state.taskDetail != null) {
              final task = state.taskDetail;

              final assingUser = userCubit.getUserById(task?.assignTo ?? "");
              final createdUser = userCubit.getUserById(task?.createdBy ?? "");
              return SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.containerGreyColor,
                      border: Border.all(color: AppColors.borderGrey),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TaskHeaderDetail(task: task),
                        SizedBox(height: 20),
                        Text(task?.desc ?? ""),
                        SizedBox(height: 20),
                        task?.attachments != null
                            ? Text("Attachments", style: AppTextStyles.label)
                            : SizedBox(),
                        SizedBox(height: 10),
                        checkPdfExtenstion(task?.attachmentType)
                            ? pdfPreviewConatiner(
                              dbFile: task?.attachments,
                              dbFileExt: task?.attachmentType,
                              dbFileName: task?.attachmentName,
                              isEdit: false,
                            )
                            : imagePreviewContainer(
                              isEdit: false,
                              dbFile: task?.attachments,
                            ),
                        SizedBox(height: 20),
                        Row(
                          children: [
                            Text("End Date", style: AppTextStyles.label),
                            Spacer(),
                            Text(task?.endDate.goodDayDate() ?? ""),
                          ],
                        ),
                        SizedBox(height: 20),
                        Row(
                          children: [
                            Expanded(
                              child: buildStatusButton(
                                task,
                                FBAuth.auth.currentUser?.uid ?? "",
                                true,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Center(
                          child: Text("Task created by ${createdUser?.name}"),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            } else if (state.isLoading) {
              return CircularProgressIndicator();
            } else {
              return Text("mmm");
            }
          },
        ),
      ),
    );
  }

  Widget buildStatusButton(TaskModel? task, String userId, bool isAdmin) {
    final taskFormCubit = context.read<TaskFormCubit>();
    bool assignUser = task?.assignTo == userId;
    if (task?.status.toLowerCase() == TaskStatus.pending.toLowerCase()) {
      return IgnorePointer(
        ignoring: !(task?.assignTo == userId),
        child: ElevatedButton(
          onPressed: () {
            taskFormCubit.selectStatus(TaskStatus.ongoing);
            taskFormCubit.createStatusLog(
              TaskStatus.ongoing,
              task?.docId ?? "",
              userId,
            );
          },
          child: Text("Start Task"),
        ),
      );
    }

    if (task?.status == TaskStatus.ongoing) {
      return IgnorePointer(
        ignoring: !(task?.assignTo == userId),
        child: ElevatedButton(
          onPressed: () {
            taskFormCubit.selectStatus(TaskStatus.submitted);
            taskFormCubit.createStatusLog(
              TaskStatus.submitted,
              task?.docId ?? "",
              userId,
            );
          },
          child: Text("Submit Task"),
        ),
      );
    }

    if (task?.status == TaskStatus.submitted) {
      return IgnorePointer(
        ignoring: isAdmin,
        child: ElevatedButton(
          onPressed: () {
            taskFormCubit.selectStatus(TaskStatus.approved);
            taskFormCubit.createStatusLog(
              TaskStatus.approved,
              task?.docId ?? "",
              userId,
            );
          },
          child: Text("Approve Task"),
        ),
      );
    }

    if (task?.status == TaskStatus.approved) {
      return IgnorePointer(
        ignoring: true,
        child: ElevatedButton(onPressed: () {}, child: Text("Approve Task")),
      );
    }

    return SizedBox();
  }
}
