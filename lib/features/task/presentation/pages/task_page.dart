import 'package:cp_associates/core/routes/router.dart';
import 'package:cp_associates/core/services/firebase.dart';
import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/core/widgets/responsive_widget.dart';
import 'package:cp_associates/features/auth/domain/entity/user_model.dart';
import 'package:cp_associates/features/auth/presentation/cubits/auth_cubit.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/task_cubit.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/task/presentation/pages/task_detail_page.dart';
import 'package:cp_associates/features/task/presentation/widgets/task_form.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

class TaskPage extends StatefulWidget {
  String projectId;
  TaskPage({super.key, required this.projectId});

  @override
  State<TaskPage> createState() => _TaskPageState();
}

class _TaskPageState extends State<TaskPage> {
  @override
  void initState() {
    context.read<TaskCubit>().fetchAllTask(widget.projectId);
  }

  @override
  Widget build(BuildContext context) {
    final taskCubit = context.read<TaskCubit>();
    return BlocConsumer<TaskCubit, TaskState>(
      listener: (context, state) {},
      builder: (context, state) {
        return ResponsiveWid(
          mobile: Column(
            crossAxisAlignment: CrossAxisAlignment.start,

            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    FilterContainer(
                      title: "All",
                      onFilterTap: () {
                        taskCubit.filterTaskByType(TaskTypes.All);
                      },
                      isSelected: state.selectedType == TaskTypes.All,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "My Task",
                      onFilterTap: () {
                        taskCubit.filterTaskByType(TaskTypes.mytask);
                      },
                      isSelected: state.selectedType == TaskTypes.mytask,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "Ongoing",
                      onFilterTap: () {
                        taskCubit.filterTaskByType(TaskTypes.onGoing);
                      },
                      isSelected: state.selectedType == TaskTypes.onGoing,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "Completed",
                      onFilterTap: () {
                        taskCubit.filterTaskByType(TaskTypes.completed);
                      },
                      isSelected: state.selectedType == TaskTypes.completed,
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20),
              Expanded(
                child: Stack(
                  children: [
                    TaskDetailTile(projectId: widget.projectId),

                    Align(
                      alignment: Alignment.bottomRight,
                      child: Container(
                        alignment: Alignment.bottomRight,
                        child: AddBtn(
                          text: "upload",

                          onPressed: () {
                            showModalBottomSheet(
                              context: context,
                              builder: (context) {
                                return Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: BlocProvider(
                                    create:
                                        (context) => TaskFormCubit(
                                          context.read<TaskCubit>().taskRepo,
                                        ),
                                    child: TaskForm(
                                      projectId: widget.projectId,
                                      editTask: null,
                                    ),
                                  ),
                                );
                              },
                            );
                          },
                          color: AppColors.taskBtn,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Spacer(),
              SizedBox(height: 100),
            ],
          ),
        );
      },
    );
  }
}

class TaskDetailTile extends StatefulWidget {
  String projectId;
  TaskDetailTile({super.key, required this.projectId});

  @override
  State<TaskDetailTile> createState() => _TaskDetailTileState();
}

class _TaskDetailTileState extends State<TaskDetailTile> {
  @override
  Widget build(BuildContext context) {
    final taskCubit = context.read<TaskCubit>();
    return BlocConsumer<TaskCubit, TaskState>(
      listener: (context, state) {
        if (state.message.isNotEmpty) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(state.message)));
        }
      },
      builder: (context, state) {
        if (state.task.isNotEmpty) {
          List<TaskModel> filteredtasks = [];
          if (state.selectedType == TaskTypes.All) {
            filteredtasks = state.task;
          } else if (state.selectedType == TaskTypes.mytask) {
            filteredtasks =
                state.task.where((task) {
                  return task.assignTo.contains(
                    FBAuth.auth.currentUser?.uid ?? "",
                  );
                }).toList();
          } else if (state.selectedType == TaskTypes.onGoing) {
            filteredtasks =
                state.task.where((task) {
                  return task.status.contains(TaskTypes.onGoing);
                }).toList();
          } else {
            filteredtasks =
                state.task.where((task) {
                  return task.status.contains(TaskTypes.completed);
                }).toList();
          }
          return filteredtasks.isEmpty
              ? Center(child: Text("No Task Avaliable"))
              : SingleChildScrollView(
                child: Column(
                  spacing: 15,
                  children: [
                    ...List.generate(filteredtasks.length, (index) {
                      final task = filteredtasks[index];

                      return GestureDetector(
                        onLongPress: () {
                          showConfirmDeletDialog(
                            context,
                            () => taskCubit.deletTask(task.docId),
                          );
                        },
                        onTap: () {
                          kIsWeb
                              ? context.go("${Routes.taskDetail}/${task.docId}")
                              : context.push(
                                "${Routes.taskDetail}/${task.docId}",
                              );
                        },
                        onDoubleTap: () {
                          showModalBottomSheet(
                            context: context,
                            builder: (context) {
                              return Padding(
                                padding: const EdgeInsets.all(16),
                                child: BlocProvider(
                                  create:
                                      (context) => TaskFormCubit(
                                        context.read<TaskCubit>().taskRepo,
                                      ),
                                  child: TaskForm(
                                    projectId: widget.projectId,
                                    editTask: task,
                                  ),
                                ),
                              );
                            },
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: AppColors.containerGreyColor,
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: AppColors.borderGrey),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TaskHeaderDetail(task: task),
                              SizedBox(height: 5),
                              Text(task.desc),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              );
        } else if (state.isLoading) {
          return Center(child: CircularProgressIndicator());
        } else {
          return Center(child: Text("No Task Avaliable"));
        }
      },
    );
  }
}

class TaskHeaderDetail extends StatefulWidget {
  const TaskHeaderDetail({
    super.key,
    //  required this.user,
    required this.task,
  });

  // final UserModel? user;
  final TaskModel? task;

  @override
  State<TaskHeaderDetail> createState() => _TaskHeaderDetailState();
}

class _TaskHeaderDetailState extends State<TaskHeaderDetail> {
  @override
  Widget build(BuildContext context) {
    final userCubit = context.read<UserCubit>();
    final user = userCubit.getUserById(widget.task?.assignTo ?? "");
    return Row(
      // crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(15),
          child: Text(
            user?.name[0].toUpperCase() ?? '',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: AppColors.secondary,
          ),
        ),
        SizedBox(width: 15),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(user?.name ?? ""),
            Row(
              children: [
                Text(
                  widget.task?.status ?? "",
                  style: TextStyle(fontSize: 12, color: AppColors.grey2),
                ),
                SizedBox(width: 5),
                Container(
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.grey2,
                  ),
                  child: Text(''),
                ),
                SizedBox(width: 5),

                Text("overdue"),
              ],
            ),
          ],
        ),
        Spacer(),
        Container(
          padding: EdgeInsets.all(5),

          child: Text(
            widget.task?.priority ?? "",
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: priorityStatusColor(widget.task?.priority ?? ''),
          ),
        ),
      ],
    );
  }
}
