import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:cp_associates/core/utils/const.dart';
import 'package:cp_associates/core/utils/helpers.dart';
import 'package:cp_associates/core/widgets/buttons.dart';
import 'package:cp_associates/core/widgets/custom_textfield.dart';
import 'package:cp_associates/core/widgets/file_preview.dart';
import 'package:cp_associates/core/widgets/filter_container.dart';
import 'package:cp_associates/features/task/domain/entity/task_model.dart';
import 'package:cp_associates/features/task/presentation/cubit/taskform_cubit.dart';
import 'package:cp_associates/features/users/presentation/cubit/user_cubit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_dropdown/multi_dropdown.dart';

class TaskForm extends StatefulWidget {
  TaskModel? editTask;
  String projectId;
  TaskForm({super.key, required this.projectId, required this.editTask});

  @override
  State<TaskForm> createState() => _TaskFormState();
}

class _TaskFormState extends State<TaskForm> {
  final controller = MultiSelectController<String>();

  @override
  void initState() {
    // TODO: implement initState
    // context.read<UserCubit>().fetchAllUsers();
    context.read<TaskFormCubit>().initializeForm(widget.editTask);
  }

  @override
  Widget build(BuildContext context) {
    final taskFormCubit = context.read<TaskFormCubit>();
    return BlocBuilder<TaskFormCubit, TaskFormState>(
      builder: (context, state) {
        return SingleChildScrollView(
          child: Form(
            key: taskFormCubit.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      icon: Icon(CupertinoIcons.xmark),
                    ),
                    Text("Add Task", style: AppTextStyles.heading2),
                    Spacer(),
                    PrimaryButton(
                      isLoading: state.isLoading,
                      text: "Save",
                      onPressed: () {
                        taskFormCubit.submit(
                          widget.editTask,
                          widget.projectId,
                          context,
                        );
                      },

                      width: 100,
                      height: 36,
                    ),
                  ],
                ),
                SizedBox(height: 10),
                CustomTextField(
                  controller: taskFormCubit.titleController,
                  hintText: "task name",
                  title: "Title",
                ),
                SizedBox(height: 10),
                CustomTextField(
                  controller: taskFormCubit.descController,
                  hintText: "task desc",
                  title: "Description",
                ),
                SizedBox(height: 10),
                Text("Assign Users"),
                SizedBox(height: 10),
                BlocBuilder<UserCubit, UserState>(
                  builder: (context, userState) {
                    if (userState.users.isNotEmpty) {
                      final users = userState.users;

                      return DropdownButtonFormField<String>(
                        value:
                            state.selectedUser.isEmpty
                                ? null
                                : state.selectedUser,
                        decoration: InputDecoration(
                          hintText: "Assign to",
                          border: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.black87),
                          ),
                        ),
                        items:
                            users.map((user) {
                              return DropdownMenuItem<String>(
                                value: user.docId,
                                child: Text(user.name),
                              );
                            }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            taskFormCubit.updateSelectedUser(value);
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a user';
                          }
                          return null;
                        },
                      );
                    } else {
                      return Text("No users available");
                    }
                  },
                ),

                SizedBox(height: 10),

                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Start date"),
                    SizedBox(height: 10),
                    TextFormField(
                      readOnly: true,
                      onTap: () {
                        taskFormCubit.selectStartDate(context);
                      },
                      decoration: InputDecoration(
                        hintText:
                            state.startDate != null
                                ? state.startDate?.goodDayDate()
                                : "start date",
                        suffixIcon: IconButton(
                          onPressed: () {
                            taskFormCubit.selectStartDate(context);
                          },
                          icon: Icon(CupertinoIcons.calendar),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.grey2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        hintStyle: TextStyle(color: AppColors.grey2),

                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      initialValue: state.startDate?.goodDayDate(),
                      validator: (value) {
                        if (state.startDate == null) {
                          return "date is required";
                        }
                        return null;
                      },
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("End date"),
                    SizedBox(height: 10),
                    TextFormField(
                      // controller: controller,
                      decoration: InputDecoration(
                        suffixIcon: IconButton(
                          onPressed: () {
                            taskFormCubit.selectEndtDate(context);
                          },
                          icon: Icon(CupertinoIcons.calendar),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.grey2),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        hintText:
                            state.endDate != null
                                ? state.endDate?.goodDayDate()
                                : "end date",
                        hintStyle: TextStyle(color: AppColors.grey2),

                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      initialValue: state.endDate?.goodDayDate(),

                      validator: (value) {
                        if (state.endDate == null) {
                          return "date is required";
                        }
                        return null;
                      },
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text("Status"),
                    SizedBox(height: 5),
                    DropdownButtonHideUnderline(
                      child: DropdownButtonFormField(
                        value: state.taskStatus,
                        validator:
                            (value) =>
                                value == null || value.isEmpty
                                    ? 'Enter Project Status'
                                    : null,
                        decoration: InputDecoration(
                          enabledBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: AppColors.grey2),
                          ),
                          hintText: "task status",
                          hintStyle: AppTextStyles.hintText,

                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        items: const [
                          DropdownMenuItem(
                            value: TaskStatus.pending,
                            child: Text(TaskStatus.pending),
                          ),
                          DropdownMenuItem(
                            value: TaskStatus.ongoing,
                            child: Text(TaskStatus.ongoing),
                          ),
                          DropdownMenuItem(
                            value: TaskStatus.submitted,
                            child: Text(TaskStatus.submitted),
                          ),
                          DropdownMenuItem(
                            value: TaskStatus.approved,
                            child: Text(TaskStatus.approved),
                          ),
                        ],
                        onChanged: (value) {
                          taskFormCubit.selectStatus(value ?? "");
                          // projectStatus = value;
                          // projectStatusUpdateAt = DateTime.now();
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Text("Priority Status"),
                SizedBox(height: 10),
                Row(
                  children: [
                    FilterContainer(
                      title: "Mid",
                      onFilterTap: () {
                        taskFormCubit.priorityStatus(TaskSPrioritytatus.mid);
                      },
                      isSelected:
                          state.priorityStatus == TaskSPrioritytatus.mid,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "High",
                      onFilterTap: () {
                        taskFormCubit.priorityStatus(TaskSPrioritytatus.high);
                      },
                      isSelected:
                          state.priorityStatus == TaskSPrioritytatus.high,
                    ),
                    SizedBox(width: 10),
                    FilterContainer(
                      title: "Low",
                      onFilterTap: () {
                        taskFormCubit.priorityStatus(TaskSPrioritytatus.low);
                      },
                      isSelected:
                          state.priorityStatus == TaskSPrioritytatus.low,
                    ),
                  ],
                ),
                SizedBox(height: 10),
                Text("Attachments"),
                SizedBox(height: 10),
                Center(child: buildFilePreview(context, state)),

                // if (state.selectedFile != null)
                //   Center(child: buildFilePreview(context, state))
                // else if (widget.editTask?.attachments != null)
                //   Center(
                //     child: ExistingAttachmentPreview(task: widget.editTask),
                //   ),
                SizedBox(height: 10),
                GestureDetector(
                  onTap: () async => taskFormCubit.pickFile(context),
                  child: AbsorbPointer(
                    child: TextFormField(
                      readOnly: true,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        hintText: "Upload Document (image, pdf) *",
                        prefixIcon: Icon(CupertinoIcons.up_arrow),
                        hintStyle: TextStyle(color: Colors.grey),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                GestureDetector(
                  onTap: () {},
                  child: AbsorbPointer(
                    child: TextFormField(
                      readOnly: true,
                      decoration: InputDecoration(
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        hintText: "Take new photo",
                        prefixIcon: Icon(CupertinoIcons.camera),
                        hintStyle: TextStyle(color: Colors.grey),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildFilePreview(BuildContext context, TaskFormState state) {
    //upload file
    final file = state.selectedFile;
    final extension = file?.extension?.toLowerCase() ?? "";
    //db file
    final dbFile = state.dbFile;
    final dbExt = widget.editTask?.attachmentType;
    final dbName = widget.editTask?.attachmentName;
    void onTapHandler() {
      context.read<TaskFormCubit>().viewPickFile(
        state.dbFile,
        context,
        widget.editTask?.attachmentType,
      );
    }

    return InkWell(
      onTap: () {
        widget.editTask != null
            ? onTapHandler()
            : context.read<TaskFormCubit>().viewPickFile(null, context, null);
      },
      child:
          file != null
              ? checkPdfExtenstion(extension)
                  ? pdfPreviewConatiner(
                    selectedFile: file,
                    isEdit: true,
                    onDelete: () {
                      context.read<TaskFormCubit>().deletPickFile(false);
                    },
                    onView: () {
                      context.read<TaskFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editTask?.attachmentType,
                      );
                    },
                  )
                  : imagePreviewContainer(
                    selectedFile: file,
                    isEdit: true,
                    onDelete: () {
                      context.read<TaskFormCubit>().deletPickFile(false);
                    },
                    onView: () {
                      context.read<TaskFormCubit>().viewPickFile(
                        state.dbFile,
                        context,
                        widget.editTask?.attachmentType,
                      );
                    },
                  )
              : dbFile != null
              ? checkPdfExtenstion(dbExt)
                  ? pdfPreviewConatiner(
                    dbFile: dbFile,
                    dbFileName: dbName,
                    dbFileExt: dbExt,
                    isEdit: true,
                  )
                  : imagePreviewContainer(
                    dbFile: dbFile,
                    dbFileExt: dbExt,
                    isEdit: true,
                  )
              // CachedNetworkImage(imageUrl: dbFile, width: 50, height: 50)
              : SizedBox(),
    );
  }
}
