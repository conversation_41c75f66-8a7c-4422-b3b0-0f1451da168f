import 'package:cp_associates/core/utils/const.dart';
import 'package:flutter/cupertino.dart';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:cp_associates/core/theme/app_colors.dart';

class imagePreviewContainer extends StatelessWidget {
  String? dbFile;
  String? dbFileExt;
  final SelectedImage? selectedFile;
  Function? onDelete;
  Function? onView;
  bool isEdit;
  imagePreviewContainer({
    super.key,
    this.selectedFile,
    this.onDelete,
    this.onView,
    this.dbFile,
    this.dbFileExt,
    required this.isEdit,
  });

  @override
  Widget build(BuildContext context) {
    return (dbFile != null || selectedFile != null)
        ? Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            dbFile != null
                ? InkWell(
                  onTap: () => onView != null ? onView!() : null,
                  child: CachedNetworkImage(
                    placeholder: (context, url) => CircularProgressIndicator(),
                    imageUrl: dbFile ?? "",
                    width: 50,
                    height: 50,
                  ),
                )
                : Image.memory(
                  selectedFile!.uInt8List,
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                ),

            isEdit
                ? Container(
                  width: 25,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    alignment: Alignment.center,
                    onPressed: () {
                      onDelete != null ? onDelete!() : null;
                    },
                    icon: Icon(
                      CupertinoIcons.delete,
                      color: Colors.black,
                      size: 20,
                    ),
                  ),
                )
                : SizedBox(),
          ],
        )
        : SizedBox();
  }
}

class pdfPreviewConatiner extends StatelessWidget {
  String? dbFile;
  String? dbFileName;
  String? dbFileExt;
  SelectedImage? selectedFile;
  bool isEdit;
  Function? onDelete;
  Function? onView;
  pdfPreviewConatiner({
    super.key,
    this.dbFile,
    this.dbFileName,
    this.dbFileExt,
    this.selectedFile,
    this.onDelete,
    this.onView,
    required this.isEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment:
          isEdit ? MainAxisAlignment.center : MainAxisAlignment.start,
      children: [
        InkWell(
          onTap: () => onView != null ? onView!() : null,
          child:
              (dbFile != null || selectedFile != null)
                  ? Container(
                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                    height: 50,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: AppColors.borderGrey,
                        width: 1.5,
                      ),
                    ),

                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.file_present, color: Colors.grey),
                        SizedBox(width: 5),

                        selectedFile != null
                            ? Text(
                              "${selectedFile?.name}.${selectedFile?.extension}",
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                              ),
                            )
                            : dbFile != null
                            ? Text(
                              "${dbFileName}.${dbFileExt}",
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                              ),
                            )
                            : SizedBox(),
                      ],
                    ),
                  )
                  : SizedBox(),
        ),
        isEdit
            ? Container(
              width: 25,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                alignment: Alignment.center,
                onPressed: () {
                  onDelete != null ? onDelete!() : null;
                },
                icon: Icon(
                  CupertinoIcons.delete,
                  color: Colors.black,
                  size: 20,
                ),
              ),
            )
            : SizedBox(),
      ],
    );
  }
}

class OtherFilePreviewContainer extends StatelessWidget {
  String? dbFile;
  String? dbFileName;
  String? dbFileExt;
  SelectedImage? selectedFile;
  bool isEdit;
  Function? onDelete;
  Function? onView;
  OtherFilePreviewContainer({
    super.key,
    this.dbFile,
    this.dbFileName,
    this.dbFileExt,
    this.selectedFile,
    this.onDelete,
    this.onView,
    required this.isEdit,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment:
          isEdit ? MainAxisAlignment.center : MainAxisAlignment.start,
      children: [
        InkWell(
          onTap: () => onView != null ? onView!() : null,
          child:
              (dbFile != null || selectedFile != null)
                  ? Container(
                    padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                    height: 50,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: AppColors.borderGrey,
                        width: 1.5,
                      ),
                    ),

                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(CupertinoIcons.doc_chart, color: Colors.grey),
                        SizedBox(width: 5),

                        selectedFile != null
                            ? Text(
                              "${selectedFile?.name}.${selectedFile?.extension}",
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                              ),
                            )
                            : dbFile != null
                            ? Text(
                              "${dbFileName}",
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 12,
                              ),
                            )
                            : SizedBox(),
                      ],
                    ),
                  )
                  : SizedBox(),
        ),
        isEdit
            ? Container(
              width: 25,
              decoration: BoxDecoration(
                color: Colors.white,
                shape: BoxShape.circle,
              ),
              child: IconButton(
                alignment: Alignment.center,
                onPressed: () {
                  onDelete != null ? onDelete!() : null;
                },
                icon: Icon(
                  CupertinoIcons.delete,
                  color: Colors.black,
                  size: 20,
                ),
              ),
            )
            : SizedBox(),
      ],
    );
  }
}

// enum FileType { image, pdf, other }

class FilePreviewContainer extends StatelessWidget {
  final String fileType; // <- using your docTypes strings
  final String? dbFile;
  final String? dbFileName;
  final String? dbFileExt;
  final SelectedImage? selectedFile;
  final bool isEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onView;

  const FilePreviewContainer({
    super.key,
    required this.fileType,
    this.dbFile,
    this.dbFileName,
    this.dbFileExt,
    this.selectedFile,
    required this.isEdit,
    this.onDelete,
    this.onView,
  });

  @override
  Widget build(BuildContext context) {
    if (dbFile == null && selectedFile == null) return const SizedBox();

    late Widget fileDisplay;

    if (fileType == docTypes.Images) {
      fileDisplay =
          dbFile != null
              ? InkWell(
                onTap: onView,
                child: CachedNetworkImage(
                  imageUrl: dbFile!,
                  placeholder:
                      (context, url) => const CircularProgressIndicator(),
                  width: 50,
                  height: 50,
                  fit: BoxFit.cover,
                ),
              )
              : Image.memory(
                selectedFile!.uInt8List,
                width: 50,
                height: 50,
                fit: BoxFit.cover,
              );
    } else {
      IconData icon =
          fileType == docTypes.PDF
              ? Icons.picture_as_pdf
              : CupertinoIcons.doc_chart;

      final label =
          selectedFile != null
              ? '${selectedFile?.name}.${selectedFile?.extension}'
              : dbFileName ?? 'Unnamed File';

      fileDisplay = Container(
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        height: 50,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color: AppColors.borderGrey, width: 1.5),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.grey),
            const SizedBox(width: 5),
            Text(
              label,
              style: const TextStyle(color: Colors.black, fontSize: 12),
            ),
          ],
        ),
      );
    }

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment:
          isEdit ? MainAxisAlignment.center : MainAxisAlignment.start,
      children: [
        InkWell(onTap: onView, child: fileDisplay),
        if (isEdit)
          Container(
            width: 25,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: IconButton(
              padding: EdgeInsets.zero,
              alignment: Alignment.center,
              icon: const Icon(
                CupertinoIcons.delete,
                size: 20,
                color: Colors.black,
              ),
              onPressed: onDelete,
            ),
          ),
      ],
    );
  }
}
