import 'package:cp_associates/core/theme/app_colors.dart';
import 'package:cp_associates/core/theme/app_text_styles.dart';
import 'package:flutter/material.dart';

class CustomTextField extends StatelessWidget {
  CustomTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
  });

  final TextEditingController controller;
  String hintText;
  String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        TextFormField(
          controller: controller,

          decoration: InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.grey2),
              borderRadius: BorderRadius.circular(10),
            ),
            hintText: hintText,
            hintStyle: TextStyle(color: AppColors.grey2),

            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return title.isEmpty
                  ? "this field is required"
                  : "Enter ${title}";
            }
            return null;
          },
          // validator:
          //     (value) =>
          //         value == null || value.isEmpty
          //             ? 'this field is required'
          //             : null,
        ),
      ],
    );
  }
}

class CustomNumTextField extends StatelessWidget {
  CustomNumTextField({
    super.key,
    required this.controller,
    required this.hintText,
    required this.title,
  });

  final TextEditingController controller;
  final String hintText;
  final String title;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        title.isEmpty ? SizedBox() : Text(title),
        SizedBox(height: 5),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(
                10,
              ), // Use your AppColors.grey2
            ),
            hintText: hintText,
            hintStyle: TextStyle(
              color: AppColors.grey2,
            ), // Use your AppTextStyles.label
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Enter $title';
            }
            if (double.tryParse(value) == null) {
              return 'Enter a valid number';
            }
            return null;
          },
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }
}
