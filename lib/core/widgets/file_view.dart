import 'dart:typed_data';
import 'package:cp_associates/core/services/image_picker.dart';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';
import 'package:cached_network_image/cached_network_image.dart';

class FileView extends StatelessWidget {
  final String? dbFile;
  final String? dbFileExt;
  final SelectedImage? selectedFile;

  FileView({super.key, this.dbFile, this.selectedFile, this.dbFileExt});

  @override
  Widget build(BuildContext context) {
    Widget content;

    if (selectedFile != null) {
      if ((selectedFile!.extension ?? '').toLowerCase() == 'pdf') {
        content = SfPdfViewer.memory(selectedFile!.uInt8List);
      } else {
        content = Image.memory(selectedFile!.uInt8List);
      }
    } else if (dbFile != null && dbFileExt != null) {
      if (dbFileExt!.toLowerCase() == 'pdf') {
        content = SfPdfViewer.network(dbFile ?? "");
      } else {
        content = CachedNetworkImage(imageUrl: dbFile ?? "", fit: BoxFit.cover);
      }
    } else {
      content = const Center(child: Text("No file to preview."));
    }

    return Scaffold(
      appBar: AppBar(title: const Text("View File")),
      body: Center(child: content),
    );
  }
}
