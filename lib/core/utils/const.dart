class Role {
  static const String designer = 'designer';
  static const String supervisor = 'supervisor';
}

class ProjectStatus {
  static const String active = 'Active';
  static const String onHold = 'on-Hold';
  static const String finished = 'Finished';
}

class TaskStatus {
  static const String pending = 'Pending';
  static const String ongoing = 'Ongoing';
  static const String submitted = 'Submitted';
  static const String approved = 'Approved';
}

class TaskSPrioritytatus {
  static const String low = 'Low';
  static const String mid = 'Mid';
  static const String high = 'High';
}

class TaskTypes {
  static const String All = 'All';
  static const String mytask = 'My Task';
  static const String completed = 'Completed';
  static const String onGoing = 'Ongoing';
}

class docTypes {
  static const String All = 'All';
  static const String PDF = 'PDF';
  static const String Images = 'Images';
  static const String Others = 'Others';
}

final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
