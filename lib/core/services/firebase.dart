import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FBAuth {
  static final auth = FirebaseAuth.instance;
}

class FBFireStore {
  static final fb = FirebaseFirestore.instance;
  static final users = fb.collection('users');
  static final projects = fb.collection('projects');
  static final activities = fb.collection('activities');
  static final tasks = fb.collection('tasks');
  static final documents = fb.collection('documents');
  static final bills = fb.collection('bills');
  static final settings = fb.collection('settings').doc("sets");
}

class FBStorage {
  static final fbstore = FirebaseStorage.instance;
  static final bills = fbstore.ref().child('bills');
  static final project = fbstore.ref().child('project');
  static final documents = fbstore.ref().child('documents');
  static final tasks = fbstore.ref().child('tasks');
  static final activity = fbstore.ref().child('activity');
}

class FBFunctions {
  static final ff = FirebaseFunctions.instance;
}
