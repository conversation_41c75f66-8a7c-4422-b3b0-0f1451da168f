<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>logFormatVersion</key>
	<integer>11</integer>
	<key>logs</key>
	<dict>
		<key>0F32E003-925A-449D-A8DE-A2DD79E9F117</key>
		<dict>
			<key>className</key>
			<string>IDECommandLineBuildLog</string>
			<key>documentTypeString</key>
			<string>&lt;nil&gt;</string>
			<key>domainType</key>
			<string>Xcode.IDEActivityLogDomainType.BuildLog</string>
			<key>fileName</key>
			<string>0F32E003-925A-449D-A8DE-A2DD79E9F117.xcactivitylog</string>
			<key>hasPrimaryLog</key>
			<true/>
			<key>primaryObservable</key>
			<dict>
				<key>highLevelStatus</key>
				<string>S</string>
				<key>totalNumberOfAnalyzerIssues</key>
				<integer>0</integer>
				<key>totalNumberOfErrors</key>
				<integer>0</integer>
				<key>totalNumberOfTestFailures</key>
				<integer>0</integer>
				<key>totalNumberOfWarnings</key>
				<integer>0</integer>
			</dict>
			<key>schemeIdentifier-containerName</key>
			<string>Runner project</string>
			<key>schemeIdentifier-schemeName</key>
			<string>Flutter Assemble</string>
			<key>schemeIdentifier-sharedScheme</key>
			<integer>1</integer>
			<key>signature</key>
			<string>Cleaning workspace Runner with scheme Flutter Assemble</string>
			<key>timeStartedRecording</key>
			<real>772696447.91725695</real>
			<key>timeStoppedRecording</key>
			<real>772696448.16160905</real>
			<key>title</key>
			<string>Cleaning workspace Runner with scheme Flutter Assemble</string>
			<key>uniqueIdentifier</key>
			<string>0F32E003-925A-449D-A8DE-A2DD79E9F117</string>
		</dict>
	</dict>
</dict>
</plist>
